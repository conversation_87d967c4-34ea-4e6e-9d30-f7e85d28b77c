@import "tailwindcss";

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply antialiased;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-bold;
  }
}

@layer components {
  .container-custom {
    @apply container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
  }

  .btn {
    @apply inline-flex items-center justify-center rounded-md px-5 py-3 text-base font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }

  .btn-secondary {
    @apply bg-amber-500 text-white hover:bg-amber-600 focus:ring-amber-400;
  }

  .btn-outline {
    @apply border border-slate-300 bg-transparent hover:bg-slate-50 focus:ring-slate-500;
  }

  .section-title {
    @apply text-3xl sm:text-4xl font-bold mb-4 text-center text-slate-800;
  }

  .section-subtitle {
    @apply text-xl text-center max-w-2xl mx-auto mb-12 text-slate-600;
  }

  .card {
    @apply bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-300;
  }

  .fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }

  .fade-in.visible {
    opacity: 1;
    transform: translateY(0);
  }
}
