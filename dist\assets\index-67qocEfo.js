(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const c of document.querySelectorAll('link[rel="modulepreload"]'))r(c);new MutationObserver(c=>{for(const d of c)if(d.type==="childList")for(const f of d.addedNodes)f.tagName==="LINK"&&f.rel==="modulepreload"&&r(f)}).observe(document,{childList:!0,subtree:!0});function u(c){const d={};return c.integrity&&(d.integrity=c.integrity),c.referrerPolicy&&(d.referrerPolicy=c.referrerPolicy),c.crossOrigin==="use-credentials"?d.credentials="include":c.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function r(c){if(c.ep)return;c.ep=!0;const d=u(c);fetch(c.href,d)}})();var xo={exports:{}},ll={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gm;function A1(){if(Gm)return ll;Gm=1;var a=Symbol.for("react.transitional.element"),l=Symbol.for("react.fragment");function u(r,c,d){var f=null;if(d!==void 0&&(f=""+d),c.key!==void 0&&(f=""+c.key),"key"in c){d={};for(var m in c)m!=="key"&&(d[m]=c[m])}else d=c;return c=d.ref,{$$typeof:a,type:r,key:f,ref:c!==void 0?c:null,props:d}}return ll.Fragment=l,ll.jsx=u,ll.jsxs=u,ll}var Xm;function E1(){return Xm||(Xm=1,xo.exports=A1()),xo.exports}var _=E1(),Ao={exports:{}},st={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zm;function M1(){if(Zm)return st;Zm=1;var a=Symbol.for("react.transitional.element"),l=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),f=Symbol.for("react.context"),m=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),b=Symbol.iterator;function T(x){return x===null||typeof x!="object"?null:(x=b&&x[b]||x["@@iterator"],typeof x=="function"?x:null)}var C={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},j=Object.assign,L={};function Z(x,U,K){this.props=x,this.context=U,this.refs=L,this.updater=K||C}Z.prototype.isReactComponent={},Z.prototype.setState=function(x,U){if(typeof x!="object"&&typeof x!="function"&&x!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,x,U,"setState")},Z.prototype.forceUpdate=function(x){this.updater.enqueueForceUpdate(this,x,"forceUpdate")};function Y(){}Y.prototype=Z.prototype;function Q(x,U,K){this.props=x,this.context=U,this.refs=L,this.updater=K||C}var q=Q.prototype=new Y;q.constructor=Q,j(q,Z.prototype),q.isPureReactComponent=!0;var tt=Array.isArray,H={H:null,A:null,T:null,S:null,V:null},W=Object.prototype.hasOwnProperty;function nt(x,U,K,X,$,ht){return K=ht.ref,{$$typeof:a,type:x,key:U,ref:K!==void 0?K:null,props:ht}}function J(x,U){return nt(x.type,U,void 0,void 0,void 0,x.props)}function yt(x){return typeof x=="object"&&x!==null&&x.$$typeof===a}function _t(x){var U={"=":"=0",":":"=2"};return"$"+x.replace(/[=:]/g,function(K){return U[K]})}var Xt=/\/+/g;function qt(x,U){return typeof x=="object"&&x!==null&&x.key!=null?_t(""+x.key):U.toString(36)}function Je(){}function He(x){switch(x.status){case"fulfilled":return x.value;case"rejected":throw x.reason;default:switch(typeof x.status=="string"?x.then(Je,Je):(x.status="pending",x.then(function(U){x.status==="pending"&&(x.status="fulfilled",x.value=U)},function(U){x.status==="pending"&&(x.status="rejected",x.reason=U)})),x.status){case"fulfilled":return x.value;case"rejected":throw x.reason}}throw x}function Pt(x,U,K,X,$){var ht=typeof x;(ht==="undefined"||ht==="boolean")&&(x=null);var lt=!1;if(x===null)lt=!0;else switch(ht){case"bigint":case"string":case"number":lt=!0;break;case"object":switch(x.$$typeof){case a:case l:lt=!0;break;case v:return lt=x._init,Pt(lt(x._payload),U,K,X,$)}}if(lt)return $=$(x),lt=X===""?"."+qt(x,0):X,tt($)?(K="",lt!=null&&(K=lt.replace(Xt,"$&/")+"/"),Pt($,U,K,"",function(mn){return mn})):$!=null&&(yt($)&&($=J($,K+($.key==null||x&&x.key===$.key?"":(""+$.key).replace(Xt,"$&/")+"/")+lt)),U.push($)),1;lt=0;var fe=X===""?".":X+":";if(tt(x))for(var Mt=0;Mt<x.length;Mt++)X=x[Mt],ht=fe+qt(X,Mt),lt+=Pt(X,U,K,ht,$);else if(Mt=T(x),typeof Mt=="function")for(x=Mt.call(x),Mt=0;!(X=x.next()).done;)X=X.value,ht=fe+qt(X,Mt++),lt+=Pt(X,U,K,ht,$);else if(ht==="object"){if(typeof x.then=="function")return Pt(He(x),U,K,X,$);throw U=String(x),Error("Objects are not valid as a React child (found: "+(U==="[object Object]"?"object with keys {"+Object.keys(x).join(", ")+"}":U)+"). If you meant to render a collection of children, use an array instead.")}return lt}function V(x,U,K){if(x==null)return x;var X=[],$=0;return Pt(x,X,"","",function(ht){return U.call(K,ht,$++)}),X}function B(x){if(x._status===-1){var U=x._result;U=U(),U.then(function(K){(x._status===0||x._status===-1)&&(x._status=1,x._result=K)},function(K){(x._status===0||x._status===-1)&&(x._status=2,x._result=K)}),x._status===-1&&(x._status=0,x._result=U)}if(x._status===1)return x._result.default;throw x._result}var P=typeof reportError=="function"?reportError:function(x){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var U=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof x=="object"&&x!==null&&typeof x.message=="string"?String(x.message):String(x),error:x});if(!window.dispatchEvent(U))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",x);return}console.error(x)};function mt(){}return st.Children={map:V,forEach:function(x,U,K){V(x,function(){U.apply(this,arguments)},K)},count:function(x){var U=0;return V(x,function(){U++}),U},toArray:function(x){return V(x,function(U){return U})||[]},only:function(x){if(!yt(x))throw Error("React.Children.only expected to receive a single React element child.");return x}},st.Component=Z,st.Fragment=u,st.Profiler=c,st.PureComponent=Q,st.StrictMode=r,st.Suspense=y,st.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=H,st.__COMPILER_RUNTIME={__proto__:null,c:function(x){return H.H.useMemoCache(x)}},st.cache=function(x){return function(){return x.apply(null,arguments)}},st.cloneElement=function(x,U,K){if(x==null)throw Error("The argument must be a React element, but you passed "+x+".");var X=j({},x.props),$=x.key,ht=void 0;if(U!=null)for(lt in U.ref!==void 0&&(ht=void 0),U.key!==void 0&&($=""+U.key),U)!W.call(U,lt)||lt==="key"||lt==="__self"||lt==="__source"||lt==="ref"&&U.ref===void 0||(X[lt]=U[lt]);var lt=arguments.length-2;if(lt===1)X.children=K;else if(1<lt){for(var fe=Array(lt),Mt=0;Mt<lt;Mt++)fe[Mt]=arguments[Mt+2];X.children=fe}return nt(x.type,$,void 0,void 0,ht,X)},st.createContext=function(x){return x={$$typeof:f,_currentValue:x,_currentValue2:x,_threadCount:0,Provider:null,Consumer:null},x.Provider=x,x.Consumer={$$typeof:d,_context:x},x},st.createElement=function(x,U,K){var X,$={},ht=null;if(U!=null)for(X in U.key!==void 0&&(ht=""+U.key),U)W.call(U,X)&&X!=="key"&&X!=="__self"&&X!=="__source"&&($[X]=U[X]);var lt=arguments.length-2;if(lt===1)$.children=K;else if(1<lt){for(var fe=Array(lt),Mt=0;Mt<lt;Mt++)fe[Mt]=arguments[Mt+2];$.children=fe}if(x&&x.defaultProps)for(X in lt=x.defaultProps,lt)$[X]===void 0&&($[X]=lt[X]);return nt(x,ht,void 0,void 0,null,$)},st.createRef=function(){return{current:null}},st.forwardRef=function(x){return{$$typeof:m,render:x}},st.isValidElement=yt,st.lazy=function(x){return{$$typeof:v,_payload:{_status:-1,_result:x},_init:B}},st.memo=function(x,U){return{$$typeof:p,type:x,compare:U===void 0?null:U}},st.startTransition=function(x){var U=H.T,K={};H.T=K;try{var X=x(),$=H.S;$!==null&&$(K,X),typeof X=="object"&&X!==null&&typeof X.then=="function"&&X.then(mt,P)}catch(ht){P(ht)}finally{H.T=U}},st.unstable_useCacheRefresh=function(){return H.H.useCacheRefresh()},st.use=function(x){return H.H.use(x)},st.useActionState=function(x,U,K){return H.H.useActionState(x,U,K)},st.useCallback=function(x,U){return H.H.useCallback(x,U)},st.useContext=function(x){return H.H.useContext(x)},st.useDebugValue=function(){},st.useDeferredValue=function(x,U){return H.H.useDeferredValue(x,U)},st.useEffect=function(x,U,K){var X=H.H;if(typeof K=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return X.useEffect(x,U)},st.useId=function(){return H.H.useId()},st.useImperativeHandle=function(x,U,K){return H.H.useImperativeHandle(x,U,K)},st.useInsertionEffect=function(x,U){return H.H.useInsertionEffect(x,U)},st.useLayoutEffect=function(x,U){return H.H.useLayoutEffect(x,U)},st.useMemo=function(x,U){return H.H.useMemo(x,U)},st.useOptimistic=function(x,U){return H.H.useOptimistic(x,U)},st.useReducer=function(x,U,K){return H.H.useReducer(x,U,K)},st.useRef=function(x){return H.H.useRef(x)},st.useState=function(x){return H.H.useState(x)},st.useSyncExternalStore=function(x,U,K){return H.H.useSyncExternalStore(x,U,K)},st.useTransition=function(){return H.H.useTransition()},st.version="19.1.0",st}var Qm;function hc(){return Qm||(Qm=1,Ao.exports=M1()),Ao.exports}var G=hc(),Eo={exports:{}},sl={},Mo={exports:{}},Do={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Km;function D1(){return Km||(Km=1,function(a){function l(V,B){var P=V.length;V.push(B);t:for(;0<P;){var mt=P-1>>>1,x=V[mt];if(0<c(x,B))V[mt]=B,V[P]=x,P=mt;else break t}}function u(V){return V.length===0?null:V[0]}function r(V){if(V.length===0)return null;var B=V[0],P=V.pop();if(P!==B){V[0]=P;t:for(var mt=0,x=V.length,U=x>>>1;mt<U;){var K=2*(mt+1)-1,X=V[K],$=K+1,ht=V[$];if(0>c(X,P))$<x&&0>c(ht,X)?(V[mt]=ht,V[$]=P,mt=$):(V[mt]=X,V[K]=P,mt=K);else if($<x&&0>c(ht,P))V[mt]=ht,V[$]=P,mt=$;else break t}}return B}function c(V,B){var P=V.sortIndex-B.sortIndex;return P!==0?P:V.id-B.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;a.unstable_now=function(){return d.now()}}else{var f=Date,m=f.now();a.unstable_now=function(){return f.now()-m}}var y=[],p=[],v=1,b=null,T=3,C=!1,j=!1,L=!1,Z=!1,Y=typeof setTimeout=="function"?setTimeout:null,Q=typeof clearTimeout=="function"?clearTimeout:null,q=typeof setImmediate<"u"?setImmediate:null;function tt(V){for(var B=u(p);B!==null;){if(B.callback===null)r(p);else if(B.startTime<=V)r(p),B.sortIndex=B.expirationTime,l(y,B);else break;B=u(p)}}function H(V){if(L=!1,tt(V),!j)if(u(y)!==null)j=!0,W||(W=!0,qt());else{var B=u(p);B!==null&&Pt(H,B.startTime-V)}}var W=!1,nt=-1,J=5,yt=-1;function _t(){return Z?!0:!(a.unstable_now()-yt<J)}function Xt(){if(Z=!1,W){var V=a.unstable_now();yt=V;var B=!0;try{t:{j=!1,L&&(L=!1,Q(nt),nt=-1),C=!0;var P=T;try{e:{for(tt(V),b=u(y);b!==null&&!(b.expirationTime>V&&_t());){var mt=b.callback;if(typeof mt=="function"){b.callback=null,T=b.priorityLevel;var x=mt(b.expirationTime<=V);if(V=a.unstable_now(),typeof x=="function"){b.callback=x,tt(V),B=!0;break e}b===u(y)&&r(y),tt(V)}else r(y);b=u(y)}if(b!==null)B=!0;else{var U=u(p);U!==null&&Pt(H,U.startTime-V),B=!1}}break t}finally{b=null,T=P,C=!1}B=void 0}}finally{B?qt():W=!1}}}var qt;if(typeof q=="function")qt=function(){q(Xt)};else if(typeof MessageChannel<"u"){var Je=new MessageChannel,He=Je.port2;Je.port1.onmessage=Xt,qt=function(){He.postMessage(null)}}else qt=function(){Y(Xt,0)};function Pt(V,B){nt=Y(function(){V(a.unstable_now())},B)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(V){V.callback=null},a.unstable_forceFrameRate=function(V){0>V||125<V?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):J=0<V?Math.floor(1e3/V):5},a.unstable_getCurrentPriorityLevel=function(){return T},a.unstable_next=function(V){switch(T){case 1:case 2:case 3:var B=3;break;default:B=T}var P=T;T=B;try{return V()}finally{T=P}},a.unstable_requestPaint=function(){Z=!0},a.unstable_runWithPriority=function(V,B){switch(V){case 1:case 2:case 3:case 4:case 5:break;default:V=3}var P=T;T=V;try{return B()}finally{T=P}},a.unstable_scheduleCallback=function(V,B,P){var mt=a.unstable_now();switch(typeof P=="object"&&P!==null?(P=P.delay,P=typeof P=="number"&&0<P?mt+P:mt):P=mt,V){case 1:var x=-1;break;case 2:x=250;break;case 5:x=1073741823;break;case 4:x=1e4;break;default:x=5e3}return x=P+x,V={id:v++,callback:B,priorityLevel:V,startTime:P,expirationTime:x,sortIndex:-1},P>mt?(V.sortIndex=P,l(p,V),u(y)===null&&V===u(p)&&(L?(Q(nt),nt=-1):L=!0,Pt(H,P-mt))):(V.sortIndex=x,l(y,V),j||C||(j=!0,W||(W=!0,qt()))),V},a.unstable_shouldYield=_t,a.unstable_wrapCallback=function(V){var B=T;return function(){var P=T;T=B;try{return V.apply(this,arguments)}finally{T=P}}}}(Do)),Do}var km;function R1(){return km||(km=1,Mo.exports=D1()),Mo.exports}var Ro={exports:{}},ee={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Pm;function O1(){if(Pm)return ee;Pm=1;var a=hc();function l(y){var p="https://react.dev/errors/"+y;if(1<arguments.length){p+="?args[]="+encodeURIComponent(arguments[1]);for(var v=2;v<arguments.length;v++)p+="&args[]="+encodeURIComponent(arguments[v])}return"Minified React error #"+y+"; visit "+p+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(){}var r={d:{f:u,r:function(){throw Error(l(522))},D:u,C:u,L:u,m:u,X:u,S:u,M:u},p:0,findDOMNode:null},c=Symbol.for("react.portal");function d(y,p,v){var b=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:c,key:b==null?null:""+b,children:y,containerInfo:p,implementation:v}}var f=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function m(y,p){if(y==="font")return"";if(typeof p=="string")return p==="use-credentials"?p:""}return ee.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,ee.createPortal=function(y,p){var v=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!p||p.nodeType!==1&&p.nodeType!==9&&p.nodeType!==11)throw Error(l(299));return d(y,p,null,v)},ee.flushSync=function(y){var p=f.T,v=r.p;try{if(f.T=null,r.p=2,y)return y()}finally{f.T=p,r.p=v,r.d.f()}},ee.preconnect=function(y,p){typeof y=="string"&&(p?(p=p.crossOrigin,p=typeof p=="string"?p==="use-credentials"?p:"":void 0):p=null,r.d.C(y,p))},ee.prefetchDNS=function(y){typeof y=="string"&&r.d.D(y)},ee.preinit=function(y,p){if(typeof y=="string"&&p&&typeof p.as=="string"){var v=p.as,b=m(v,p.crossOrigin),T=typeof p.integrity=="string"?p.integrity:void 0,C=typeof p.fetchPriority=="string"?p.fetchPriority:void 0;v==="style"?r.d.S(y,typeof p.precedence=="string"?p.precedence:void 0,{crossOrigin:b,integrity:T,fetchPriority:C}):v==="script"&&r.d.X(y,{crossOrigin:b,integrity:T,fetchPriority:C,nonce:typeof p.nonce=="string"?p.nonce:void 0})}},ee.preinitModule=function(y,p){if(typeof y=="string")if(typeof p=="object"&&p!==null){if(p.as==null||p.as==="script"){var v=m(p.as,p.crossOrigin);r.d.M(y,{crossOrigin:v,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0})}}else p==null&&r.d.M(y)},ee.preload=function(y,p){if(typeof y=="string"&&typeof p=="object"&&p!==null&&typeof p.as=="string"){var v=p.as,b=m(v,p.crossOrigin);r.d.L(y,v,{crossOrigin:b,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0,type:typeof p.type=="string"?p.type:void 0,fetchPriority:typeof p.fetchPriority=="string"?p.fetchPriority:void 0,referrerPolicy:typeof p.referrerPolicy=="string"?p.referrerPolicy:void 0,imageSrcSet:typeof p.imageSrcSet=="string"?p.imageSrcSet:void 0,imageSizes:typeof p.imageSizes=="string"?p.imageSizes:void 0,media:typeof p.media=="string"?p.media:void 0})}},ee.preloadModule=function(y,p){if(typeof y=="string")if(p){var v=m(p.as,p.crossOrigin);r.d.m(y,{as:typeof p.as=="string"&&p.as!=="script"?p.as:void 0,crossOrigin:v,integrity:typeof p.integrity=="string"?p.integrity:void 0})}else r.d.m(y)},ee.requestFormReset=function(y){r.d.r(y)},ee.unstable_batchedUpdates=function(y,p){return y(p)},ee.useFormState=function(y,p,v){return f.H.useFormState(y,p,v)},ee.useFormStatus=function(){return f.H.useHostTransitionStatus()},ee.version="19.1.0",ee}var Jm;function C1(){if(Jm)return Ro.exports;Jm=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(l){console.error(l)}}return a(),Ro.exports=O1(),Ro.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fm;function _1(){if(Fm)return sl;Fm=1;var a=R1(),l=hc(),u=C1();function r(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function d(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function f(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function m(t){if(d(t)!==t)throw Error(r(188))}function y(t){var e=t.alternate;if(!e){if(e=d(t),e===null)throw Error(r(188));return e!==t?null:t}for(var n=t,i=e;;){var s=n.return;if(s===null)break;var o=s.alternate;if(o===null){if(i=s.return,i!==null){n=i;continue}break}if(s.child===o.child){for(o=s.child;o;){if(o===n)return m(s),t;if(o===i)return m(s),e;o=o.sibling}throw Error(r(188))}if(n.return!==i.return)n=s,i=o;else{for(var h=!1,g=s.child;g;){if(g===n){h=!0,n=s,i=o;break}if(g===i){h=!0,i=s,n=o;break}g=g.sibling}if(!h){for(g=o.child;g;){if(g===n){h=!0,n=o,i=s;break}if(g===i){h=!0,i=o,n=s;break}g=g.sibling}if(!h)throw Error(r(189))}}if(n.alternate!==i)throw Error(r(190))}if(n.tag!==3)throw Error(r(188));return n.stateNode.current===n?t:e}function p(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=p(t),e!==null)return e;t=t.sibling}return null}var v=Object.assign,b=Symbol.for("react.element"),T=Symbol.for("react.transitional.element"),C=Symbol.for("react.portal"),j=Symbol.for("react.fragment"),L=Symbol.for("react.strict_mode"),Z=Symbol.for("react.profiler"),Y=Symbol.for("react.provider"),Q=Symbol.for("react.consumer"),q=Symbol.for("react.context"),tt=Symbol.for("react.forward_ref"),H=Symbol.for("react.suspense"),W=Symbol.for("react.suspense_list"),nt=Symbol.for("react.memo"),J=Symbol.for("react.lazy"),yt=Symbol.for("react.activity"),_t=Symbol.for("react.memo_cache_sentinel"),Xt=Symbol.iterator;function qt(t){return t===null||typeof t!="object"?null:(t=Xt&&t[Xt]||t["@@iterator"],typeof t=="function"?t:null)}var Je=Symbol.for("react.client.reference");function He(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Je?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case j:return"Fragment";case Z:return"Profiler";case L:return"StrictMode";case H:return"Suspense";case W:return"SuspenseList";case yt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case C:return"Portal";case q:return(t.displayName||"Context")+".Provider";case Q:return(t._context.displayName||"Context")+".Consumer";case tt:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case nt:return e=t.displayName||null,e!==null?e:He(t.type)||"Memo";case J:e=t._payload,t=t._init;try{return He(t(e))}catch{}}return null}var Pt=Array.isArray,V=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,B=u.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,P={pending:!1,data:null,method:null,action:null},mt=[],x=-1;function U(t){return{current:t}}function K(t){0>x||(t.current=mt[x],mt[x]=null,x--)}function X(t,e){x++,mt[x]=t.current,t.current=e}var $=U(null),ht=U(null),lt=U(null),fe=U(null);function Mt(t,e){switch(X(lt,e),X(ht,t),X($,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?ym(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=ym(e),t=gm(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}K($),X($,t)}function mn(){K($),K(ht),K(lt)}function su(t){t.memoizedState!==null&&X(fe,t);var e=$.current,n=gm(e,t.type);e!==n&&(X(ht,t),X($,n))}function Dl(t){ht.current===t&&(K($),K(ht)),fe.current===t&&(K(fe),tl._currentValue=P)}var uu=Object.prototype.hasOwnProperty,ru=a.unstable_scheduleCallback,ou=a.unstable_cancelCallback,eg=a.unstable_shouldYield,ng=a.unstable_requestPaint,qe=a.unstable_now,ag=a.unstable_getCurrentPriorityLevel,Jc=a.unstable_ImmediatePriority,Fc=a.unstable_UserBlockingPriority,Rl=a.unstable_NormalPriority,ig=a.unstable_LowPriority,Wc=a.unstable_IdlePriority,lg=a.log,sg=a.unstable_setDisableYieldValue,ri=null,he=null;function pn(t){if(typeof lg=="function"&&sg(t),he&&typeof he.setStrictMode=="function")try{he.setStrictMode(ri,t)}catch{}}var de=Math.clz32?Math.clz32:og,ug=Math.log,rg=Math.LN2;function og(t){return t>>>=0,t===0?32:31-(ug(t)/rg|0)|0}var Ol=256,Cl=4194304;function Xn(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function _l(t,e,n){var i=t.pendingLanes;if(i===0)return 0;var s=0,o=t.suspendedLanes,h=t.pingedLanes;t=t.warmLanes;var g=i&134217727;return g!==0?(i=g&~o,i!==0?s=Xn(i):(h&=g,h!==0?s=Xn(h):n||(n=g&~t,n!==0&&(s=Xn(n))))):(g=i&~o,g!==0?s=Xn(g):h!==0?s=Xn(h):n||(n=i&~t,n!==0&&(s=Xn(n)))),s===0?0:e!==0&&e!==s&&(e&o)===0&&(o=s&-s,n=e&-e,o>=n||o===32&&(n&4194048)!==0)?e:s}function oi(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function cg(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function $c(){var t=Ol;return Ol<<=1,(Ol&4194048)===0&&(Ol=256),t}function Ic(){var t=Cl;return Cl<<=1,(Cl&62914560)===0&&(Cl=4194304),t}function cu(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function ci(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function fg(t,e,n,i,s,o){var h=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var g=t.entanglements,S=t.expirationTimes,D=t.hiddenUpdates;for(n=h&~n;0<n;){var N=31-de(n),w=1<<N;g[N]=0,S[N]=-1;var R=D[N];if(R!==null)for(D[N]=null,N=0;N<R.length;N++){var O=R[N];O!==null&&(O.lane&=-536870913)}n&=~w}i!==0&&tf(t,i,0),o!==0&&s===0&&t.tag!==0&&(t.suspendedLanes|=o&~(h&~e))}function tf(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var i=31-de(e);t.entangledLanes|=e,t.entanglements[i]=t.entanglements[i]|1073741824|n&4194090}function ef(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var i=31-de(n),s=1<<i;s&e|t[i]&e&&(t[i]|=e),n&=~s}}function fu(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function hu(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function nf(){var t=B.p;return t!==0?t:(t=window.event,t===void 0?32:Um(t.type))}function hg(t,e){var n=B.p;try{return B.p=t,e()}finally{B.p=n}}var yn=Math.random().toString(36).slice(2),It="__reactFiber$"+yn,le="__reactProps$"+yn,da="__reactContainer$"+yn,du="__reactEvents$"+yn,dg="__reactListeners$"+yn,mg="__reactHandles$"+yn,af="__reactResources$"+yn,fi="__reactMarker$"+yn;function mu(t){delete t[It],delete t[le],delete t[du],delete t[dg],delete t[mg]}function ma(t){var e=t[It];if(e)return e;for(var n=t.parentNode;n;){if(e=n[da]||n[It]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=Tm(t);t!==null;){if(n=t[It])return n;t=Tm(t)}return e}t=n,n=t.parentNode}return null}function pa(t){if(t=t[It]||t[da]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function hi(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(r(33))}function ya(t){var e=t[af];return e||(e=t[af]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Zt(t){t[fi]=!0}var lf=new Set,sf={};function Zn(t,e){ga(t,e),ga(t+"Capture",e)}function ga(t,e){for(sf[t]=e,t=0;t<e.length;t++)lf.add(e[t])}var pg=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),uf={},rf={};function yg(t){return uu.call(rf,t)?!0:uu.call(uf,t)?!1:pg.test(t)?rf[t]=!0:(uf[t]=!0,!1)}function Nl(t,e,n){if(yg(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var i=e.toLowerCase().slice(0,5);if(i!=="data-"&&i!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function Vl(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function Fe(t,e,n,i){if(i===null)t.removeAttribute(n);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+i)}}var pu,of;function va(t){if(pu===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);pu=e&&e[1]||"",of=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+pu+t+of}var yu=!1;function gu(t,e){if(!t||yu)return"";yu=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var i={DetermineComponentFrameRoot:function(){try{if(e){var w=function(){throw Error()};if(Object.defineProperty(w.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(w,[])}catch(O){var R=O}Reflect.construct(t,[],w)}else{try{w.call()}catch(O){R=O}t.call(w.prototype)}}else{try{throw Error()}catch(O){R=O}(w=t())&&typeof w.catch=="function"&&w.catch(function(){})}}catch(O){if(O&&R&&typeof O.stack=="string")return[O.stack,R.stack]}return[null,null]}};i.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var s=Object.getOwnPropertyDescriptor(i.DetermineComponentFrameRoot,"name");s&&s.configurable&&Object.defineProperty(i.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var o=i.DetermineComponentFrameRoot(),h=o[0],g=o[1];if(h&&g){var S=h.split(`
`),D=g.split(`
`);for(s=i=0;i<S.length&&!S[i].includes("DetermineComponentFrameRoot");)i++;for(;s<D.length&&!D[s].includes("DetermineComponentFrameRoot");)s++;if(i===S.length||s===D.length)for(i=S.length-1,s=D.length-1;1<=i&&0<=s&&S[i]!==D[s];)s--;for(;1<=i&&0<=s;i--,s--)if(S[i]!==D[s]){if(i!==1||s!==1)do if(i--,s--,0>s||S[i]!==D[s]){var N=`
`+S[i].replace(" at new "," at ");return t.displayName&&N.includes("<anonymous>")&&(N=N.replace("<anonymous>",t.displayName)),N}while(1<=i&&0<=s);break}}}finally{yu=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?va(n):""}function gg(t){switch(t.tag){case 26:case 27:case 5:return va(t.type);case 16:return va("Lazy");case 13:return va("Suspense");case 19:return va("SuspenseList");case 0:case 15:return gu(t.type,!1);case 11:return gu(t.type.render,!1);case 1:return gu(t.type,!0);case 31:return va("Activity");default:return""}}function cf(t){try{var e="";do e+=gg(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Te(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function ff(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function vg(t){var e=ff(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),i=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,o=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return s.call(this)},set:function(h){i=""+h,o.call(this,h)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return i},setValue:function(h){i=""+h},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function zl(t){t._valueTracker||(t._valueTracker=vg(t))}function hf(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),i="";return t&&(i=ff(t)?t.checked?"true":"false":t.value),t=i,t!==n?(e.setValue(t),!0):!1}function wl(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var bg=/[\n"\\]/g;function xe(t){return t.replace(bg,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function vu(t,e,n,i,s,o,h,g){t.name="",h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?t.type=h:t.removeAttribute("type"),e!=null?h==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Te(e)):t.value!==""+Te(e)&&(t.value=""+Te(e)):h!=="submit"&&h!=="reset"||t.removeAttribute("value"),e!=null?bu(t,h,Te(e)):n!=null?bu(t,h,Te(n)):i!=null&&t.removeAttribute("value"),s==null&&o!=null&&(t.defaultChecked=!!o),s!=null&&(t.checked=s&&typeof s!="function"&&typeof s!="symbol"),g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"?t.name=""+Te(g):t.removeAttribute("name")}function df(t,e,n,i,s,o,h,g){if(o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(t.type=o),e!=null||n!=null){if(!(o!=="submit"&&o!=="reset"||e!=null))return;n=n!=null?""+Te(n):"",e=e!=null?""+Te(e):n,g||e===t.value||(t.value=e),t.defaultValue=e}i=i??s,i=typeof i!="function"&&typeof i!="symbol"&&!!i,t.checked=g?t.checked:!!i,t.defaultChecked=!!i,h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"&&(t.name=h)}function bu(t,e,n){e==="number"&&wl(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function ba(t,e,n,i){if(t=t.options,e){e={};for(var s=0;s<n.length;s++)e["$"+n[s]]=!0;for(n=0;n<t.length;n++)s=e.hasOwnProperty("$"+t[n].value),t[n].selected!==s&&(t[n].selected=s),s&&i&&(t[n].defaultSelected=!0)}else{for(n=""+Te(n),e=null,s=0;s<t.length;s++){if(t[s].value===n){t[s].selected=!0,i&&(t[s].defaultSelected=!0);return}e!==null||t[s].disabled||(e=t[s])}e!==null&&(e.selected=!0)}}function mf(t,e,n){if(e!=null&&(e=""+Te(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+Te(n):""}function pf(t,e,n,i){if(e==null){if(i!=null){if(n!=null)throw Error(r(92));if(Pt(i)){if(1<i.length)throw Error(r(93));i=i[0]}n=i}n==null&&(n=""),e=n}n=Te(e),t.defaultValue=n,i=t.textContent,i===n&&i!==""&&i!==null&&(t.value=i)}function Sa(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var Sg=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function yf(t,e,n){var i=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?i?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":i?t.setProperty(e,n):typeof n!="number"||n===0||Sg.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function gf(t,e,n){if(e!=null&&typeof e!="object")throw Error(r(62));if(t=t.style,n!=null){for(var i in n)!n.hasOwnProperty(i)||e!=null&&e.hasOwnProperty(i)||(i.indexOf("--")===0?t.setProperty(i,""):i==="float"?t.cssFloat="":t[i]="");for(var s in e)i=e[s],e.hasOwnProperty(s)&&n[s]!==i&&yf(t,s,i)}else for(var o in e)e.hasOwnProperty(o)&&yf(t,o,e[o])}function Su(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Tg=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),xg=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function jl(t){return xg.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Tu=null;function xu(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Ta=null,xa=null;function vf(t){var e=pa(t);if(e&&(t=e.stateNode)){var n=t[le]||null;t:switch(t=e.stateNode,e.type){case"input":if(vu(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+xe(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var i=n[e];if(i!==t&&i.form===t.form){var s=i[le]||null;if(!s)throw Error(r(90));vu(i,s.value,s.defaultValue,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name)}}for(e=0;e<n.length;e++)i=n[e],i.form===t.form&&hf(i)}break t;case"textarea":mf(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&ba(t,!!n.multiple,e,!1)}}}var Au=!1;function bf(t,e,n){if(Au)return t(e,n);Au=!0;try{var i=t(e);return i}finally{if(Au=!1,(Ta!==null||xa!==null)&&(Ss(),Ta&&(e=Ta,t=xa,xa=Ta=null,vf(e),t)))for(e=0;e<t.length;e++)vf(t[e])}}function di(t,e){var n=t.stateNode;if(n===null)return null;var i=n[le]||null;if(i===null)return null;n=i[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(t=t.type,i=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!i;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(r(231,e,typeof n));return n}var We=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Eu=!1;if(We)try{var mi={};Object.defineProperty(mi,"passive",{get:function(){Eu=!0}}),window.addEventListener("test",mi,mi),window.removeEventListener("test",mi,mi)}catch{Eu=!1}var gn=null,Mu=null,Ul=null;function Sf(){if(Ul)return Ul;var t,e=Mu,n=e.length,i,s="value"in gn?gn.value:gn.textContent,o=s.length;for(t=0;t<n&&e[t]===s[t];t++);var h=n-t;for(i=1;i<=h&&e[n-i]===s[o-i];i++);return Ul=s.slice(t,1<i?1-i:void 0)}function Bl(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Ll(){return!0}function Tf(){return!1}function se(t){function e(n,i,s,o,h){this._reactName=n,this._targetInst=s,this.type=i,this.nativeEvent=o,this.target=h,this.currentTarget=null;for(var g in t)t.hasOwnProperty(g)&&(n=t[g],this[g]=n?n(o):o[g]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Ll:Tf,this.isPropagationStopped=Tf,this}return v(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ll)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ll)},persist:function(){},isPersistent:Ll}),e}var Qn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Hl=se(Qn),pi=v({},Qn,{view:0,detail:0}),Ag=se(pi),Du,Ru,yi,ql=v({},pi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Cu,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==yi&&(yi&&t.type==="mousemove"?(Du=t.screenX-yi.screenX,Ru=t.screenY-yi.screenY):Ru=Du=0,yi=t),Du)},movementY:function(t){return"movementY"in t?t.movementY:Ru}}),xf=se(ql),Eg=v({},ql,{dataTransfer:0}),Mg=se(Eg),Dg=v({},pi,{relatedTarget:0}),Ou=se(Dg),Rg=v({},Qn,{animationName:0,elapsedTime:0,pseudoElement:0}),Og=se(Rg),Cg=v({},Qn,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),_g=se(Cg),Ng=v({},Qn,{data:0}),Af=se(Ng),Vg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},zg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},wg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function jg(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=wg[t])?!!e[t]:!1}function Cu(){return jg}var Ug=v({},pi,{key:function(t){if(t.key){var e=Vg[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Bl(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?zg[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Cu,charCode:function(t){return t.type==="keypress"?Bl(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Bl(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Bg=se(Ug),Lg=v({},ql,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ef=se(Lg),Hg=v({},pi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Cu}),qg=se(Hg),Yg=v({},Qn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Gg=se(Yg),Xg=v({},ql,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Zg=se(Xg),Qg=v({},Qn,{newState:0,oldState:0}),Kg=se(Qg),kg=[9,13,27,32],_u=We&&"CompositionEvent"in window,gi=null;We&&"documentMode"in document&&(gi=document.documentMode);var Pg=We&&"TextEvent"in window&&!gi,Mf=We&&(!_u||gi&&8<gi&&11>=gi),Df=" ",Rf=!1;function Of(t,e){switch(t){case"keyup":return kg.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Cf(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Aa=!1;function Jg(t,e){switch(t){case"compositionend":return Cf(e);case"keypress":return e.which!==32?null:(Rf=!0,Df);case"textInput":return t=e.data,t===Df&&Rf?null:t;default:return null}}function Fg(t,e){if(Aa)return t==="compositionend"||!_u&&Of(t,e)?(t=Sf(),Ul=Mu=gn=null,Aa=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Mf&&e.locale!=="ko"?null:e.data;default:return null}}var Wg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function _f(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Wg[t.type]:e==="textarea"}function Nf(t,e,n,i){Ta?xa?xa.push(i):xa=[i]:Ta=i,e=Ds(e,"onChange"),0<e.length&&(n=new Hl("onChange","change",null,n,i),t.push({event:n,listeners:e}))}var vi=null,bi=null;function $g(t){fm(t,0)}function Yl(t){var e=hi(t);if(hf(e))return t}function Vf(t,e){if(t==="change")return e}var zf=!1;if(We){var Nu;if(We){var Vu="oninput"in document;if(!Vu){var wf=document.createElement("div");wf.setAttribute("oninput","return;"),Vu=typeof wf.oninput=="function"}Nu=Vu}else Nu=!1;zf=Nu&&(!document.documentMode||9<document.documentMode)}function jf(){vi&&(vi.detachEvent("onpropertychange",Uf),bi=vi=null)}function Uf(t){if(t.propertyName==="value"&&Yl(bi)){var e=[];Nf(e,bi,t,xu(t)),bf($g,e)}}function Ig(t,e,n){t==="focusin"?(jf(),vi=e,bi=n,vi.attachEvent("onpropertychange",Uf)):t==="focusout"&&jf()}function tv(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Yl(bi)}function ev(t,e){if(t==="click")return Yl(e)}function nv(t,e){if(t==="input"||t==="change")return Yl(e)}function av(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var me=typeof Object.is=="function"?Object.is:av;function Si(t,e){if(me(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),i=Object.keys(e);if(n.length!==i.length)return!1;for(i=0;i<n.length;i++){var s=n[i];if(!uu.call(e,s)||!me(t[s],e[s]))return!1}return!0}function Bf(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Lf(t,e){var n=Bf(t);t=0;for(var i;n;){if(n.nodeType===3){if(i=t+n.textContent.length,t<=e&&i>=e)return{node:n,offset:e-t};t=i}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=Bf(n)}}function Hf(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Hf(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function qf(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=wl(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=wl(t.document)}return e}function zu(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var iv=We&&"documentMode"in document&&11>=document.documentMode,Ea=null,wu=null,Ti=null,ju=!1;function Yf(t,e,n){var i=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ju||Ea==null||Ea!==wl(i)||(i=Ea,"selectionStart"in i&&zu(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),Ti&&Si(Ti,i)||(Ti=i,i=Ds(wu,"onSelect"),0<i.length&&(e=new Hl("onSelect","select",null,e,n),t.push({event:e,listeners:i}),e.target=Ea)))}function Kn(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var Ma={animationend:Kn("Animation","AnimationEnd"),animationiteration:Kn("Animation","AnimationIteration"),animationstart:Kn("Animation","AnimationStart"),transitionrun:Kn("Transition","TransitionRun"),transitionstart:Kn("Transition","TransitionStart"),transitioncancel:Kn("Transition","TransitionCancel"),transitionend:Kn("Transition","TransitionEnd")},Uu={},Gf={};We&&(Gf=document.createElement("div").style,"AnimationEvent"in window||(delete Ma.animationend.animation,delete Ma.animationiteration.animation,delete Ma.animationstart.animation),"TransitionEvent"in window||delete Ma.transitionend.transition);function kn(t){if(Uu[t])return Uu[t];if(!Ma[t])return t;var e=Ma[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in Gf)return Uu[t]=e[n];return t}var Xf=kn("animationend"),Zf=kn("animationiteration"),Qf=kn("animationstart"),lv=kn("transitionrun"),sv=kn("transitionstart"),uv=kn("transitioncancel"),Kf=kn("transitionend"),kf=new Map,Bu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Bu.push("scrollEnd");function ze(t,e){kf.set(t,e),Zn(e,[t])}var Pf=new WeakMap;function Ae(t,e){if(typeof t=="object"&&t!==null){var n=Pf.get(t);return n!==void 0?n:(e={value:t,source:e,stack:cf(e)},Pf.set(t,e),e)}return{value:t,source:e,stack:cf(e)}}var Ee=[],Da=0,Lu=0;function Gl(){for(var t=Da,e=Lu=Da=0;e<t;){var n=Ee[e];Ee[e++]=null;var i=Ee[e];Ee[e++]=null;var s=Ee[e];Ee[e++]=null;var o=Ee[e];if(Ee[e++]=null,i!==null&&s!==null){var h=i.pending;h===null?s.next=s:(s.next=h.next,h.next=s),i.pending=s}o!==0&&Jf(n,s,o)}}function Xl(t,e,n,i){Ee[Da++]=t,Ee[Da++]=e,Ee[Da++]=n,Ee[Da++]=i,Lu|=i,t.lanes|=i,t=t.alternate,t!==null&&(t.lanes|=i)}function Hu(t,e,n,i){return Xl(t,e,n,i),Zl(t)}function Ra(t,e){return Xl(t,null,null,e),Zl(t)}function Jf(t,e,n){t.lanes|=n;var i=t.alternate;i!==null&&(i.lanes|=n);for(var s=!1,o=t.return;o!==null;)o.childLanes|=n,i=o.alternate,i!==null&&(i.childLanes|=n),o.tag===22&&(t=o.stateNode,t===null||t._visibility&1||(s=!0)),t=o,o=o.return;return t.tag===3?(o=t.stateNode,s&&e!==null&&(s=31-de(n),t=o.hiddenUpdates,i=t[s],i===null?t[s]=[e]:i.push(e),e.lane=n|536870912),o):null}function Zl(t){if(50<Ki)throw Ki=0,Qr=null,Error(r(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Oa={};function rv(t,e,n,i){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function pe(t,e,n,i){return new rv(t,e,n,i)}function qu(t){return t=t.prototype,!(!t||!t.isReactComponent)}function $e(t,e){var n=t.alternate;return n===null?(n=pe(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function Ff(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Ql(t,e,n,i,s,o){var h=0;if(i=t,typeof t=="function")qu(t)&&(h=1);else if(typeof t=="string")h=c1(t,n,$.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case yt:return t=pe(31,n,e,s),t.elementType=yt,t.lanes=o,t;case j:return Pn(n.children,s,o,e);case L:h=8,s|=24;break;case Z:return t=pe(12,n,e,s|2),t.elementType=Z,t.lanes=o,t;case H:return t=pe(13,n,e,s),t.elementType=H,t.lanes=o,t;case W:return t=pe(19,n,e,s),t.elementType=W,t.lanes=o,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case Y:case q:h=10;break t;case Q:h=9;break t;case tt:h=11;break t;case nt:h=14;break t;case J:h=16,i=null;break t}h=29,n=Error(r(130,t===null?"null":typeof t,"")),i=null}return e=pe(h,n,e,s),e.elementType=t,e.type=i,e.lanes=o,e}function Pn(t,e,n,i){return t=pe(7,t,i,e),t.lanes=n,t}function Yu(t,e,n){return t=pe(6,t,null,e),t.lanes=n,t}function Gu(t,e,n){return e=pe(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Ca=[],_a=0,Kl=null,kl=0,Me=[],De=0,Jn=null,Ie=1,tn="";function Fn(t,e){Ca[_a++]=kl,Ca[_a++]=Kl,Kl=t,kl=e}function Wf(t,e,n){Me[De++]=Ie,Me[De++]=tn,Me[De++]=Jn,Jn=t;var i=Ie;t=tn;var s=32-de(i)-1;i&=~(1<<s),n+=1;var o=32-de(e)+s;if(30<o){var h=s-s%5;o=(i&(1<<h)-1).toString(32),i>>=h,s-=h,Ie=1<<32-de(e)+s|n<<s|i,tn=o+t}else Ie=1<<o|n<<s|i,tn=t}function Xu(t){t.return!==null&&(Fn(t,1),Wf(t,1,0))}function Zu(t){for(;t===Kl;)Kl=Ca[--_a],Ca[_a]=null,kl=Ca[--_a],Ca[_a]=null;for(;t===Jn;)Jn=Me[--De],Me[De]=null,tn=Me[--De],Me[De]=null,Ie=Me[--De],Me[De]=null}var ae=null,Nt=null,pt=!1,Wn=null,Ye=!1,Qu=Error(r(519));function $n(t){var e=Error(r(418,""));throw Ei(Ae(e,t)),Qu}function $f(t){var e=t.stateNode,n=t.type,i=t.memoizedProps;switch(e[It]=t,e[le]=i,n){case"dialog":ct("cancel",e),ct("close",e);break;case"iframe":case"object":case"embed":ct("load",e);break;case"video":case"audio":for(n=0;n<Pi.length;n++)ct(Pi[n],e);break;case"source":ct("error",e);break;case"img":case"image":case"link":ct("error",e),ct("load",e);break;case"details":ct("toggle",e);break;case"input":ct("invalid",e),df(e,i.value,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name,!0),zl(e);break;case"select":ct("invalid",e);break;case"textarea":ct("invalid",e),pf(e,i.value,i.defaultValue,i.children),zl(e)}n=i.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||i.suppressHydrationWarning===!0||pm(e.textContent,n)?(i.popover!=null&&(ct("beforetoggle",e),ct("toggle",e)),i.onScroll!=null&&ct("scroll",e),i.onScrollEnd!=null&&ct("scrollend",e),i.onClick!=null&&(e.onclick=Rs),e=!0):e=!1,e||$n(t)}function If(t){for(ae=t.return;ae;)switch(ae.tag){case 5:case 13:Ye=!1;return;case 27:case 3:Ye=!0;return;default:ae=ae.return}}function xi(t){if(t!==ae)return!1;if(!pt)return If(t),pt=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||uo(t.type,t.memoizedProps)),n=!n),n&&Nt&&$n(t),If(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(r(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){Nt=je(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}Nt=null}}else e===27?(e=Nt,zn(t.type)?(t=fo,fo=null,Nt=t):Nt=e):Nt=ae?je(t.stateNode.nextSibling):null;return!0}function Ai(){Nt=ae=null,pt=!1}function th(){var t=Wn;return t!==null&&(oe===null?oe=t:oe.push.apply(oe,t),Wn=null),t}function Ei(t){Wn===null?Wn=[t]:Wn.push(t)}var Ku=U(null),In=null,en=null;function vn(t,e,n){X(Ku,e._currentValue),e._currentValue=n}function nn(t){t._currentValue=Ku.current,K(Ku)}function ku(t,e,n){for(;t!==null;){var i=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,i!==null&&(i.childLanes|=e)):i!==null&&(i.childLanes&e)!==e&&(i.childLanes|=e),t===n)break;t=t.return}}function Pu(t,e,n,i){var s=t.child;for(s!==null&&(s.return=t);s!==null;){var o=s.dependencies;if(o!==null){var h=s.child;o=o.firstContext;t:for(;o!==null;){var g=o;o=s;for(var S=0;S<e.length;S++)if(g.context===e[S]){o.lanes|=n,g=o.alternate,g!==null&&(g.lanes|=n),ku(o.return,n,t),i||(h=null);break t}o=g.next}}else if(s.tag===18){if(h=s.return,h===null)throw Error(r(341));h.lanes|=n,o=h.alternate,o!==null&&(o.lanes|=n),ku(h,n,t),h=null}else h=s.child;if(h!==null)h.return=s;else for(h=s;h!==null;){if(h===t){h=null;break}if(s=h.sibling,s!==null){s.return=h.return,h=s;break}h=h.return}s=h}}function Mi(t,e,n,i){t=null;for(var s=e,o=!1;s!==null;){if(!o){if((s.flags&524288)!==0)o=!0;else if((s.flags&262144)!==0)break}if(s.tag===10){var h=s.alternate;if(h===null)throw Error(r(387));if(h=h.memoizedProps,h!==null){var g=s.type;me(s.pendingProps.value,h.value)||(t!==null?t.push(g):t=[g])}}else if(s===fe.current){if(h=s.alternate,h===null)throw Error(r(387));h.memoizedState.memoizedState!==s.memoizedState.memoizedState&&(t!==null?t.push(tl):t=[tl])}s=s.return}t!==null&&Pu(e,t,n,i),e.flags|=262144}function Pl(t){for(t=t.firstContext;t!==null;){if(!me(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function ta(t){In=t,en=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function te(t){return eh(In,t)}function Jl(t,e){return In===null&&ta(t),eh(t,e)}function eh(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},en===null){if(t===null)throw Error(r(308));en=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else en=en.next=e;return n}var ov=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,i){t.push(i)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},cv=a.unstable_scheduleCallback,fv=a.unstable_NormalPriority,Yt={$$typeof:q,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ju(){return{controller:new ov,data:new Map,refCount:0}}function Di(t){t.refCount--,t.refCount===0&&cv(fv,function(){t.controller.abort()})}var Ri=null,Fu=0,Na=0,Va=null;function hv(t,e){if(Ri===null){var n=Ri=[];Fu=0,Na=$r(),Va={status:"pending",value:void 0,then:function(i){n.push(i)}}}return Fu++,e.then(nh,nh),e}function nh(){if(--Fu===0&&Ri!==null){Va!==null&&(Va.status="fulfilled");var t=Ri;Ri=null,Na=0,Va=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function dv(t,e){var n=[],i={status:"pending",value:null,reason:null,then:function(s){n.push(s)}};return t.then(function(){i.status="fulfilled",i.value=e;for(var s=0;s<n.length;s++)(0,n[s])(e)},function(s){for(i.status="rejected",i.reason=s,s=0;s<n.length;s++)(0,n[s])(void 0)}),i}var ah=V.S;V.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&hv(t,e),ah!==null&&ah(t,e)};var ea=U(null);function Wu(){var t=ea.current;return t!==null?t:At.pooledCache}function Fl(t,e){e===null?X(ea,ea.current):X(ea,e.pool)}function ih(){var t=Wu();return t===null?null:{parent:Yt._currentValue,pool:t}}var Oi=Error(r(460)),lh=Error(r(474)),Wl=Error(r(542)),$u={then:function(){}};function sh(t){return t=t.status,t==="fulfilled"||t==="rejected"}function $l(){}function uh(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then($l,$l),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,oh(t),t;default:if(typeof e.status=="string")e.then($l,$l);else{if(t=At,t!==null&&100<t.shellSuspendCounter)throw Error(r(482));t=e,t.status="pending",t.then(function(i){if(e.status==="pending"){var s=e;s.status="fulfilled",s.value=i}},function(i){if(e.status==="pending"){var s=e;s.status="rejected",s.reason=i}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,oh(t),t}throw Ci=e,Oi}}var Ci=null;function rh(){if(Ci===null)throw Error(r(459));var t=Ci;return Ci=null,t}function oh(t){if(t===Oi||t===Wl)throw Error(r(483))}var bn=!1;function Iu(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function tr(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Sn(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Tn(t,e,n){var i=t.updateQueue;if(i===null)return null;if(i=i.shared,(gt&2)!==0){var s=i.pending;return s===null?e.next=e:(e.next=s.next,s.next=e),i.pending=e,e=Zl(t),Jf(t,null,n),e}return Xl(t,i,e,n),Zl(t)}function _i(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var i=e.lanes;i&=t.pendingLanes,n|=i,e.lanes=n,ef(t,n)}}function er(t,e){var n=t.updateQueue,i=t.alternate;if(i!==null&&(i=i.updateQueue,n===i)){var s=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var h={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};o===null?s=o=h:o=o.next=h,n=n.next}while(n!==null);o===null?s=o=e:o=o.next=e}else s=o=e;n={baseState:i.baseState,firstBaseUpdate:s,lastBaseUpdate:o,shared:i.shared,callbacks:i.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var nr=!1;function Ni(){if(nr){var t=Va;if(t!==null)throw t}}function Vi(t,e,n,i){nr=!1;var s=t.updateQueue;bn=!1;var o=s.firstBaseUpdate,h=s.lastBaseUpdate,g=s.shared.pending;if(g!==null){s.shared.pending=null;var S=g,D=S.next;S.next=null,h===null?o=D:h.next=D,h=S;var N=t.alternate;N!==null&&(N=N.updateQueue,g=N.lastBaseUpdate,g!==h&&(g===null?N.firstBaseUpdate=D:g.next=D,N.lastBaseUpdate=S))}if(o!==null){var w=s.baseState;h=0,N=D=S=null,g=o;do{var R=g.lane&-536870913,O=R!==g.lane;if(O?(ft&R)===R:(i&R)===R){R!==0&&R===Na&&(nr=!0),N!==null&&(N=N.next={lane:0,tag:g.tag,payload:g.payload,callback:null,next:null});t:{var at=t,I=g;R=e;var Tt=n;switch(I.tag){case 1:if(at=I.payload,typeof at=="function"){w=at.call(Tt,w,R);break t}w=at;break t;case 3:at.flags=at.flags&-65537|128;case 0:if(at=I.payload,R=typeof at=="function"?at.call(Tt,w,R):at,R==null)break t;w=v({},w,R);break t;case 2:bn=!0}}R=g.callback,R!==null&&(t.flags|=64,O&&(t.flags|=8192),O=s.callbacks,O===null?s.callbacks=[R]:O.push(R))}else O={lane:R,tag:g.tag,payload:g.payload,callback:g.callback,next:null},N===null?(D=N=O,S=w):N=N.next=O,h|=R;if(g=g.next,g===null){if(g=s.shared.pending,g===null)break;O=g,g=O.next,O.next=null,s.lastBaseUpdate=O,s.shared.pending=null}}while(!0);N===null&&(S=w),s.baseState=S,s.firstBaseUpdate=D,s.lastBaseUpdate=N,o===null&&(s.shared.lanes=0),Cn|=h,t.lanes=h,t.memoizedState=w}}function ch(t,e){if(typeof t!="function")throw Error(r(191,t));t.call(e)}function fh(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)ch(n[t],e)}var za=U(null),Il=U(0);function hh(t,e){t=cn,X(Il,t),X(za,e),cn=t|e.baseLanes}function ar(){X(Il,cn),X(za,za.current)}function ir(){cn=Il.current,K(za),K(Il)}var xn=0,ut=null,bt=null,Bt=null,ts=!1,wa=!1,na=!1,es=0,zi=0,ja=null,mv=0;function wt(){throw Error(r(321))}function lr(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!me(t[n],e[n]))return!1;return!0}function sr(t,e,n,i,s,o){return xn=o,ut=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,V.H=t===null||t.memoizedState===null?Jh:Fh,na=!1,o=n(i,s),na=!1,wa&&(o=mh(e,n,i,s)),dh(t),o}function dh(t){V.H=us;var e=bt!==null&&bt.next!==null;if(xn=0,Bt=bt=ut=null,ts=!1,zi=0,ja=null,e)throw Error(r(300));t===null||Qt||(t=t.dependencies,t!==null&&Pl(t)&&(Qt=!0))}function mh(t,e,n,i){ut=t;var s=0;do{if(wa&&(ja=null),zi=0,wa=!1,25<=s)throw Error(r(301));if(s+=1,Bt=bt=null,t.updateQueue!=null){var o=t.updateQueue;o.lastEffect=null,o.events=null,o.stores=null,o.memoCache!=null&&(o.memoCache.index=0)}V.H=Tv,o=e(n,i)}while(wa);return o}function pv(){var t=V.H,e=t.useState()[0];return e=typeof e.then=="function"?wi(e):e,t=t.useState()[0],(bt!==null?bt.memoizedState:null)!==t&&(ut.flags|=1024),e}function ur(){var t=es!==0;return es=0,t}function rr(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function or(t){if(ts){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}ts=!1}xn=0,Bt=bt=ut=null,wa=!1,zi=es=0,ja=null}function ue(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Bt===null?ut.memoizedState=Bt=t:Bt=Bt.next=t,Bt}function Lt(){if(bt===null){var t=ut.alternate;t=t!==null?t.memoizedState:null}else t=bt.next;var e=Bt===null?ut.memoizedState:Bt.next;if(e!==null)Bt=e,bt=t;else{if(t===null)throw ut.alternate===null?Error(r(467)):Error(r(310));bt=t,t={memoizedState:bt.memoizedState,baseState:bt.baseState,baseQueue:bt.baseQueue,queue:bt.queue,next:null},Bt===null?ut.memoizedState=Bt=t:Bt=Bt.next=t}return Bt}function cr(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function wi(t){var e=zi;return zi+=1,ja===null&&(ja=[]),t=uh(ja,t,e),e=ut,(Bt===null?e.memoizedState:Bt.next)===null&&(e=e.alternate,V.H=e===null||e.memoizedState===null?Jh:Fh),t}function ns(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return wi(t);if(t.$$typeof===q)return te(t)}throw Error(r(438,String(t)))}function fr(t){var e=null,n=ut.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var i=ut.alternate;i!==null&&(i=i.updateQueue,i!==null&&(i=i.memoCache,i!=null&&(e={data:i.data.map(function(s){return s.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=cr(),ut.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),i=0;i<t;i++)n[i]=_t;return e.index++,n}function an(t,e){return typeof e=="function"?e(t):e}function as(t){var e=Lt();return hr(e,bt,t)}function hr(t,e,n){var i=t.queue;if(i===null)throw Error(r(311));i.lastRenderedReducer=n;var s=t.baseQueue,o=i.pending;if(o!==null){if(s!==null){var h=s.next;s.next=o.next,o.next=h}e.baseQueue=s=o,i.pending=null}if(o=t.baseState,s===null)t.memoizedState=o;else{e=s.next;var g=h=null,S=null,D=e,N=!1;do{var w=D.lane&-536870913;if(w!==D.lane?(ft&w)===w:(xn&w)===w){var R=D.revertLane;if(R===0)S!==null&&(S=S.next={lane:0,revertLane:0,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null}),w===Na&&(N=!0);else if((xn&R)===R){D=D.next,R===Na&&(N=!0);continue}else w={lane:0,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},S===null?(g=S=w,h=o):S=S.next=w,ut.lanes|=R,Cn|=R;w=D.action,na&&n(o,w),o=D.hasEagerState?D.eagerState:n(o,w)}else R={lane:w,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},S===null?(g=S=R,h=o):S=S.next=R,ut.lanes|=w,Cn|=w;D=D.next}while(D!==null&&D!==e);if(S===null?h=o:S.next=g,!me(o,t.memoizedState)&&(Qt=!0,N&&(n=Va,n!==null)))throw n;t.memoizedState=o,t.baseState=h,t.baseQueue=S,i.lastRenderedState=o}return s===null&&(i.lanes=0),[t.memoizedState,i.dispatch]}function dr(t){var e=Lt(),n=e.queue;if(n===null)throw Error(r(311));n.lastRenderedReducer=t;var i=n.dispatch,s=n.pending,o=e.memoizedState;if(s!==null){n.pending=null;var h=s=s.next;do o=t(o,h.action),h=h.next;while(h!==s);me(o,e.memoizedState)||(Qt=!0),e.memoizedState=o,e.baseQueue===null&&(e.baseState=o),n.lastRenderedState=o}return[o,i]}function ph(t,e,n){var i=ut,s=Lt(),o=pt;if(o){if(n===void 0)throw Error(r(407));n=n()}else n=e();var h=!me((bt||s).memoizedState,n);h&&(s.memoizedState=n,Qt=!0),s=s.queue;var g=vh.bind(null,i,s,t);if(ji(2048,8,g,[t]),s.getSnapshot!==e||h||Bt!==null&&Bt.memoizedState.tag&1){if(i.flags|=2048,Ua(9,is(),gh.bind(null,i,s,n,e),null),At===null)throw Error(r(349));o||(xn&124)!==0||yh(i,e,n)}return n}function yh(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=ut.updateQueue,e===null?(e=cr(),ut.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function gh(t,e,n,i){e.value=n,e.getSnapshot=i,bh(e)&&Sh(t)}function vh(t,e,n){return n(function(){bh(e)&&Sh(t)})}function bh(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!me(t,n)}catch{return!0}}function Sh(t){var e=Ra(t,2);e!==null&&Se(e,t,2)}function mr(t){var e=ue();if(typeof t=="function"){var n=t;if(t=n(),na){pn(!0);try{n()}finally{pn(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:an,lastRenderedState:t},e}function Th(t,e,n,i){return t.baseState=n,hr(t,bt,typeof i=="function"?i:an)}function yv(t,e,n,i,s){if(ss(t))throw Error(r(485));if(t=e.action,t!==null){var o={payload:s,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(h){o.listeners.push(h)}};V.T!==null?n(!0):o.isTransition=!1,i(o),n=e.pending,n===null?(o.next=e.pending=o,xh(e,o)):(o.next=n.next,e.pending=n.next=o)}}function xh(t,e){var n=e.action,i=e.payload,s=t.state;if(e.isTransition){var o=V.T,h={};V.T=h;try{var g=n(s,i),S=V.S;S!==null&&S(h,g),Ah(t,e,g)}catch(D){pr(t,e,D)}finally{V.T=o}}else try{o=n(s,i),Ah(t,e,o)}catch(D){pr(t,e,D)}}function Ah(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(i){Eh(t,e,i)},function(i){return pr(t,e,i)}):Eh(t,e,n)}function Eh(t,e,n){e.status="fulfilled",e.value=n,Mh(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,xh(t,n)))}function pr(t,e,n){var i=t.pending;if(t.pending=null,i!==null){i=i.next;do e.status="rejected",e.reason=n,Mh(e),e=e.next;while(e!==i)}t.action=null}function Mh(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Dh(t,e){return e}function Rh(t,e){if(pt){var n=At.formState;if(n!==null){t:{var i=ut;if(pt){if(Nt){e:{for(var s=Nt,o=Ye;s.nodeType!==8;){if(!o){s=null;break e}if(s=je(s.nextSibling),s===null){s=null;break e}}o=s.data,s=o==="F!"||o==="F"?s:null}if(s){Nt=je(s.nextSibling),i=s.data==="F!";break t}}$n(i)}i=!1}i&&(e=n[0])}}return n=ue(),n.memoizedState=n.baseState=e,i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Dh,lastRenderedState:e},n.queue=i,n=Kh.bind(null,ut,i),i.dispatch=n,i=mr(!1),o=Sr.bind(null,ut,!1,i.queue),i=ue(),s={state:e,dispatch:null,action:t,pending:null},i.queue=s,n=yv.bind(null,ut,s,o,n),s.dispatch=n,i.memoizedState=t,[e,n,!1]}function Oh(t){var e=Lt();return Ch(e,bt,t)}function Ch(t,e,n){if(e=hr(t,e,Dh)[0],t=as(an)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var i=wi(e)}catch(h){throw h===Oi?Wl:h}else i=e;e=Lt();var s=e.queue,o=s.dispatch;return n!==e.memoizedState&&(ut.flags|=2048,Ua(9,is(),gv.bind(null,s,n),null)),[i,o,t]}function gv(t,e){t.action=e}function _h(t){var e=Lt(),n=bt;if(n!==null)return Ch(e,n,t);Lt(),e=e.memoizedState,n=Lt();var i=n.queue.dispatch;return n.memoizedState=t,[e,i,!1]}function Ua(t,e,n,i){return t={tag:t,create:n,deps:i,inst:e,next:null},e=ut.updateQueue,e===null&&(e=cr(),ut.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(i=n.next,n.next=t,t.next=i,e.lastEffect=t),t}function is(){return{destroy:void 0,resource:void 0}}function Nh(){return Lt().memoizedState}function ls(t,e,n,i){var s=ue();i=i===void 0?null:i,ut.flags|=t,s.memoizedState=Ua(1|e,is(),n,i)}function ji(t,e,n,i){var s=Lt();i=i===void 0?null:i;var o=s.memoizedState.inst;bt!==null&&i!==null&&lr(i,bt.memoizedState.deps)?s.memoizedState=Ua(e,o,n,i):(ut.flags|=t,s.memoizedState=Ua(1|e,o,n,i))}function Vh(t,e){ls(8390656,8,t,e)}function zh(t,e){ji(2048,8,t,e)}function wh(t,e){return ji(4,2,t,e)}function jh(t,e){return ji(4,4,t,e)}function Uh(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Bh(t,e,n){n=n!=null?n.concat([t]):null,ji(4,4,Uh.bind(null,e,t),n)}function yr(){}function Lh(t,e){var n=Lt();e=e===void 0?null:e;var i=n.memoizedState;return e!==null&&lr(e,i[1])?i[0]:(n.memoizedState=[t,e],t)}function Hh(t,e){var n=Lt();e=e===void 0?null:e;var i=n.memoizedState;if(e!==null&&lr(e,i[1]))return i[0];if(i=t(),na){pn(!0);try{t()}finally{pn(!1)}}return n.memoizedState=[i,e],i}function gr(t,e,n){return n===void 0||(xn&1073741824)!==0?t.memoizedState=e:(t.memoizedState=n,t=Gd(),ut.lanes|=t,Cn|=t,n)}function qh(t,e,n,i){return me(n,e)?n:za.current!==null?(t=gr(t,n,i),me(t,e)||(Qt=!0),t):(xn&42)===0?(Qt=!0,t.memoizedState=n):(t=Gd(),ut.lanes|=t,Cn|=t,e)}function Yh(t,e,n,i,s){var o=B.p;B.p=o!==0&&8>o?o:8;var h=V.T,g={};V.T=g,Sr(t,!1,e,n);try{var S=s(),D=V.S;if(D!==null&&D(g,S),S!==null&&typeof S=="object"&&typeof S.then=="function"){var N=dv(S,i);Ui(t,e,N,be(t))}else Ui(t,e,i,be(t))}catch(w){Ui(t,e,{then:function(){},status:"rejected",reason:w},be())}finally{B.p=o,V.T=h}}function vv(){}function vr(t,e,n,i){if(t.tag!==5)throw Error(r(476));var s=Gh(t).queue;Yh(t,s,e,P,n===null?vv:function(){return Xh(t),n(i)})}function Gh(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:P,baseState:P,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:an,lastRenderedState:P},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:an,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Xh(t){var e=Gh(t).next.queue;Ui(t,e,{},be())}function br(){return te(tl)}function Zh(){return Lt().memoizedState}function Qh(){return Lt().memoizedState}function bv(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=be();t=Sn(n);var i=Tn(e,t,n);i!==null&&(Se(i,e,n),_i(i,e,n)),e={cache:Ju()},t.payload=e;return}e=e.return}}function Sv(t,e,n){var i=be();n={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},ss(t)?kh(e,n):(n=Hu(t,e,n,i),n!==null&&(Se(n,t,i),Ph(n,e,i)))}function Kh(t,e,n){var i=be();Ui(t,e,n,i)}function Ui(t,e,n,i){var s={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(ss(t))kh(e,s);else{var o=t.alternate;if(t.lanes===0&&(o===null||o.lanes===0)&&(o=e.lastRenderedReducer,o!==null))try{var h=e.lastRenderedState,g=o(h,n);if(s.hasEagerState=!0,s.eagerState=g,me(g,h))return Xl(t,e,s,0),At===null&&Gl(),!1}catch{}finally{}if(n=Hu(t,e,s,i),n!==null)return Se(n,t,i),Ph(n,e,i),!0}return!1}function Sr(t,e,n,i){if(i={lane:2,revertLane:$r(),action:i,hasEagerState:!1,eagerState:null,next:null},ss(t)){if(e)throw Error(r(479))}else e=Hu(t,n,i,2),e!==null&&Se(e,t,2)}function ss(t){var e=t.alternate;return t===ut||e!==null&&e===ut}function kh(t,e){wa=ts=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function Ph(t,e,n){if((n&4194048)!==0){var i=e.lanes;i&=t.pendingLanes,n|=i,e.lanes=n,ef(t,n)}}var us={readContext:te,use:ns,useCallback:wt,useContext:wt,useEffect:wt,useImperativeHandle:wt,useLayoutEffect:wt,useInsertionEffect:wt,useMemo:wt,useReducer:wt,useRef:wt,useState:wt,useDebugValue:wt,useDeferredValue:wt,useTransition:wt,useSyncExternalStore:wt,useId:wt,useHostTransitionStatus:wt,useFormState:wt,useActionState:wt,useOptimistic:wt,useMemoCache:wt,useCacheRefresh:wt},Jh={readContext:te,use:ns,useCallback:function(t,e){return ue().memoizedState=[t,e===void 0?null:e],t},useContext:te,useEffect:Vh,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,ls(4194308,4,Uh.bind(null,e,t),n)},useLayoutEffect:function(t,e){return ls(4194308,4,t,e)},useInsertionEffect:function(t,e){ls(4,2,t,e)},useMemo:function(t,e){var n=ue();e=e===void 0?null:e;var i=t();if(na){pn(!0);try{t()}finally{pn(!1)}}return n.memoizedState=[i,e],i},useReducer:function(t,e,n){var i=ue();if(n!==void 0){var s=n(e);if(na){pn(!0);try{n(e)}finally{pn(!1)}}}else s=e;return i.memoizedState=i.baseState=s,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:s},i.queue=t,t=t.dispatch=Sv.bind(null,ut,t),[i.memoizedState,t]},useRef:function(t){var e=ue();return t={current:t},e.memoizedState=t},useState:function(t){t=mr(t);var e=t.queue,n=Kh.bind(null,ut,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:yr,useDeferredValue:function(t,e){var n=ue();return gr(n,t,e)},useTransition:function(){var t=mr(!1);return t=Yh.bind(null,ut,t.queue,!0,!1),ue().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var i=ut,s=ue();if(pt){if(n===void 0)throw Error(r(407));n=n()}else{if(n=e(),At===null)throw Error(r(349));(ft&124)!==0||yh(i,e,n)}s.memoizedState=n;var o={value:n,getSnapshot:e};return s.queue=o,Vh(vh.bind(null,i,o,t),[t]),i.flags|=2048,Ua(9,is(),gh.bind(null,i,o,n,e),null),n},useId:function(){var t=ue(),e=At.identifierPrefix;if(pt){var n=tn,i=Ie;n=(i&~(1<<32-de(i)-1)).toString(32)+n,e="«"+e+"R"+n,n=es++,0<n&&(e+="H"+n.toString(32)),e+="»"}else n=mv++,e="«"+e+"r"+n.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:br,useFormState:Rh,useActionState:Rh,useOptimistic:function(t){var e=ue();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=Sr.bind(null,ut,!0,n),n.dispatch=e,[t,e]},useMemoCache:fr,useCacheRefresh:function(){return ue().memoizedState=bv.bind(null,ut)}},Fh={readContext:te,use:ns,useCallback:Lh,useContext:te,useEffect:zh,useImperativeHandle:Bh,useInsertionEffect:wh,useLayoutEffect:jh,useMemo:Hh,useReducer:as,useRef:Nh,useState:function(){return as(an)},useDebugValue:yr,useDeferredValue:function(t,e){var n=Lt();return qh(n,bt.memoizedState,t,e)},useTransition:function(){var t=as(an)[0],e=Lt().memoizedState;return[typeof t=="boolean"?t:wi(t),e]},useSyncExternalStore:ph,useId:Zh,useHostTransitionStatus:br,useFormState:Oh,useActionState:Oh,useOptimistic:function(t,e){var n=Lt();return Th(n,bt,t,e)},useMemoCache:fr,useCacheRefresh:Qh},Tv={readContext:te,use:ns,useCallback:Lh,useContext:te,useEffect:zh,useImperativeHandle:Bh,useInsertionEffect:wh,useLayoutEffect:jh,useMemo:Hh,useReducer:dr,useRef:Nh,useState:function(){return dr(an)},useDebugValue:yr,useDeferredValue:function(t,e){var n=Lt();return bt===null?gr(n,t,e):qh(n,bt.memoizedState,t,e)},useTransition:function(){var t=dr(an)[0],e=Lt().memoizedState;return[typeof t=="boolean"?t:wi(t),e]},useSyncExternalStore:ph,useId:Zh,useHostTransitionStatus:br,useFormState:_h,useActionState:_h,useOptimistic:function(t,e){var n=Lt();return bt!==null?Th(n,bt,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:fr,useCacheRefresh:Qh},Ba=null,Bi=0;function rs(t){var e=Bi;return Bi+=1,Ba===null&&(Ba=[]),uh(Ba,t,e)}function Li(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function os(t,e){throw e.$$typeof===b?Error(r(525)):(t=Object.prototype.toString.call(e),Error(r(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Wh(t){var e=t._init;return e(t._payload)}function $h(t){function e(E,A){if(t){var M=E.deletions;M===null?(E.deletions=[A],E.flags|=16):M.push(A)}}function n(E,A){if(!t)return null;for(;A!==null;)e(E,A),A=A.sibling;return null}function i(E){for(var A=new Map;E!==null;)E.key!==null?A.set(E.key,E):A.set(E.index,E),E=E.sibling;return A}function s(E,A){return E=$e(E,A),E.index=0,E.sibling=null,E}function o(E,A,M){return E.index=M,t?(M=E.alternate,M!==null?(M=M.index,M<A?(E.flags|=67108866,A):M):(E.flags|=67108866,A)):(E.flags|=1048576,A)}function h(E){return t&&E.alternate===null&&(E.flags|=67108866),E}function g(E,A,M,z){return A===null||A.tag!==6?(A=Yu(M,E.mode,z),A.return=E,A):(A=s(A,M),A.return=E,A)}function S(E,A,M,z){var k=M.type;return k===j?N(E,A,M.props.children,z,M.key):A!==null&&(A.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===J&&Wh(k)===A.type)?(A=s(A,M.props),Li(A,M),A.return=E,A):(A=Ql(M.type,M.key,M.props,null,E.mode,z),Li(A,M),A.return=E,A)}function D(E,A,M,z){return A===null||A.tag!==4||A.stateNode.containerInfo!==M.containerInfo||A.stateNode.implementation!==M.implementation?(A=Gu(M,E.mode,z),A.return=E,A):(A=s(A,M.children||[]),A.return=E,A)}function N(E,A,M,z,k){return A===null||A.tag!==7?(A=Pn(M,E.mode,z,k),A.return=E,A):(A=s(A,M),A.return=E,A)}function w(E,A,M){if(typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint")return A=Yu(""+A,E.mode,M),A.return=E,A;if(typeof A=="object"&&A!==null){switch(A.$$typeof){case T:return M=Ql(A.type,A.key,A.props,null,E.mode,M),Li(M,A),M.return=E,M;case C:return A=Gu(A,E.mode,M),A.return=E,A;case J:var z=A._init;return A=z(A._payload),w(E,A,M)}if(Pt(A)||qt(A))return A=Pn(A,E.mode,M,null),A.return=E,A;if(typeof A.then=="function")return w(E,rs(A),M);if(A.$$typeof===q)return w(E,Jl(E,A),M);os(E,A)}return null}function R(E,A,M,z){var k=A!==null?A.key:null;if(typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint")return k!==null?null:g(E,A,""+M,z);if(typeof M=="object"&&M!==null){switch(M.$$typeof){case T:return M.key===k?S(E,A,M,z):null;case C:return M.key===k?D(E,A,M,z):null;case J:return k=M._init,M=k(M._payload),R(E,A,M,z)}if(Pt(M)||qt(M))return k!==null?null:N(E,A,M,z,null);if(typeof M.then=="function")return R(E,A,rs(M),z);if(M.$$typeof===q)return R(E,A,Jl(E,M),z);os(E,M)}return null}function O(E,A,M,z,k){if(typeof z=="string"&&z!==""||typeof z=="number"||typeof z=="bigint")return E=E.get(M)||null,g(A,E,""+z,k);if(typeof z=="object"&&z!==null){switch(z.$$typeof){case T:return E=E.get(z.key===null?M:z.key)||null,S(A,E,z,k);case C:return E=E.get(z.key===null?M:z.key)||null,D(A,E,z,k);case J:var rt=z._init;return z=rt(z._payload),O(E,A,M,z,k)}if(Pt(z)||qt(z))return E=E.get(M)||null,N(A,E,z,k,null);if(typeof z.then=="function")return O(E,A,M,rs(z),k);if(z.$$typeof===q)return O(E,A,M,Jl(A,z),k);os(A,z)}return null}function at(E,A,M,z){for(var k=null,rt=null,F=A,et=A=0,kt=null;F!==null&&et<M.length;et++){F.index>et?(kt=F,F=null):kt=F.sibling;var dt=R(E,F,M[et],z);if(dt===null){F===null&&(F=kt);break}t&&F&&dt.alternate===null&&e(E,F),A=o(dt,A,et),rt===null?k=dt:rt.sibling=dt,rt=dt,F=kt}if(et===M.length)return n(E,F),pt&&Fn(E,et),k;if(F===null){for(;et<M.length;et++)F=w(E,M[et],z),F!==null&&(A=o(F,A,et),rt===null?k=F:rt.sibling=F,rt=F);return pt&&Fn(E,et),k}for(F=i(F);et<M.length;et++)kt=O(F,E,et,M[et],z),kt!==null&&(t&&kt.alternate!==null&&F.delete(kt.key===null?et:kt.key),A=o(kt,A,et),rt===null?k=kt:rt.sibling=kt,rt=kt);return t&&F.forEach(function(Ln){return e(E,Ln)}),pt&&Fn(E,et),k}function I(E,A,M,z){if(M==null)throw Error(r(151));for(var k=null,rt=null,F=A,et=A=0,kt=null,dt=M.next();F!==null&&!dt.done;et++,dt=M.next()){F.index>et?(kt=F,F=null):kt=F.sibling;var Ln=R(E,F,dt.value,z);if(Ln===null){F===null&&(F=kt);break}t&&F&&Ln.alternate===null&&e(E,F),A=o(Ln,A,et),rt===null?k=Ln:rt.sibling=Ln,rt=Ln,F=kt}if(dt.done)return n(E,F),pt&&Fn(E,et),k;if(F===null){for(;!dt.done;et++,dt=M.next())dt=w(E,dt.value,z),dt!==null&&(A=o(dt,A,et),rt===null?k=dt:rt.sibling=dt,rt=dt);return pt&&Fn(E,et),k}for(F=i(F);!dt.done;et++,dt=M.next())dt=O(F,E,et,dt.value,z),dt!==null&&(t&&dt.alternate!==null&&F.delete(dt.key===null?et:dt.key),A=o(dt,A,et),rt===null?k=dt:rt.sibling=dt,rt=dt);return t&&F.forEach(function(x1){return e(E,x1)}),pt&&Fn(E,et),k}function Tt(E,A,M,z){if(typeof M=="object"&&M!==null&&M.type===j&&M.key===null&&(M=M.props.children),typeof M=="object"&&M!==null){switch(M.$$typeof){case T:t:{for(var k=M.key;A!==null;){if(A.key===k){if(k=M.type,k===j){if(A.tag===7){n(E,A.sibling),z=s(A,M.props.children),z.return=E,E=z;break t}}else if(A.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===J&&Wh(k)===A.type){n(E,A.sibling),z=s(A,M.props),Li(z,M),z.return=E,E=z;break t}n(E,A);break}else e(E,A);A=A.sibling}M.type===j?(z=Pn(M.props.children,E.mode,z,M.key),z.return=E,E=z):(z=Ql(M.type,M.key,M.props,null,E.mode,z),Li(z,M),z.return=E,E=z)}return h(E);case C:t:{for(k=M.key;A!==null;){if(A.key===k)if(A.tag===4&&A.stateNode.containerInfo===M.containerInfo&&A.stateNode.implementation===M.implementation){n(E,A.sibling),z=s(A,M.children||[]),z.return=E,E=z;break t}else{n(E,A);break}else e(E,A);A=A.sibling}z=Gu(M,E.mode,z),z.return=E,E=z}return h(E);case J:return k=M._init,M=k(M._payload),Tt(E,A,M,z)}if(Pt(M))return at(E,A,M,z);if(qt(M)){if(k=qt(M),typeof k!="function")throw Error(r(150));return M=k.call(M),I(E,A,M,z)}if(typeof M.then=="function")return Tt(E,A,rs(M),z);if(M.$$typeof===q)return Tt(E,A,Jl(E,M),z);os(E,M)}return typeof M=="string"&&M!==""||typeof M=="number"||typeof M=="bigint"?(M=""+M,A!==null&&A.tag===6?(n(E,A.sibling),z=s(A,M),z.return=E,E=z):(n(E,A),z=Yu(M,E.mode,z),z.return=E,E=z),h(E)):n(E,A)}return function(E,A,M,z){try{Bi=0;var k=Tt(E,A,M,z);return Ba=null,k}catch(F){if(F===Oi||F===Wl)throw F;var rt=pe(29,F,null,E.mode);return rt.lanes=z,rt.return=E,rt}finally{}}}var La=$h(!0),Ih=$h(!1),Re=U(null),Ge=null;function An(t){var e=t.alternate;X(Gt,Gt.current&1),X(Re,t),Ge===null&&(e===null||za.current!==null||e.memoizedState!==null)&&(Ge=t)}function td(t){if(t.tag===22){if(X(Gt,Gt.current),X(Re,t),Ge===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ge=t)}}else En()}function En(){X(Gt,Gt.current),X(Re,Re.current)}function ln(t){K(Re),Ge===t&&(Ge=null),K(Gt)}var Gt=U(0);function cs(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||co(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Tr(t,e,n,i){e=t.memoizedState,n=n(i,e),n=n==null?e:v({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var xr={enqueueSetState:function(t,e,n){t=t._reactInternals;var i=be(),s=Sn(i);s.payload=e,n!=null&&(s.callback=n),e=Tn(t,s,i),e!==null&&(Se(e,t,i),_i(e,t,i))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var i=be(),s=Sn(i);s.tag=1,s.payload=e,n!=null&&(s.callback=n),e=Tn(t,s,i),e!==null&&(Se(e,t,i),_i(e,t,i))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=be(),i=Sn(n);i.tag=2,e!=null&&(i.callback=e),e=Tn(t,i,n),e!==null&&(Se(e,t,n),_i(e,t,n))}};function ed(t,e,n,i,s,o,h){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(i,o,h):e.prototype&&e.prototype.isPureReactComponent?!Si(n,i)||!Si(s,o):!0}function nd(t,e,n,i){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,i),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,i),e.state!==t&&xr.enqueueReplaceState(e,e.state,null)}function aa(t,e){var n=e;if("ref"in e){n={};for(var i in e)i!=="ref"&&(n[i]=e[i])}if(t=t.defaultProps){n===e&&(n=v({},n));for(var s in t)n[s]===void 0&&(n[s]=t[s])}return n}var fs=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function ad(t){fs(t)}function id(t){console.error(t)}function ld(t){fs(t)}function hs(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(i){setTimeout(function(){throw i})}}function sd(t,e,n){try{var i=t.onCaughtError;i(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(s){setTimeout(function(){throw s})}}function Ar(t,e,n){return n=Sn(n),n.tag=3,n.payload={element:null},n.callback=function(){hs(t,e)},n}function ud(t){return t=Sn(t),t.tag=3,t}function rd(t,e,n,i){var s=n.type.getDerivedStateFromError;if(typeof s=="function"){var o=i.value;t.payload=function(){return s(o)},t.callback=function(){sd(e,n,i)}}var h=n.stateNode;h!==null&&typeof h.componentDidCatch=="function"&&(t.callback=function(){sd(e,n,i),typeof s!="function"&&(_n===null?_n=new Set([this]):_n.add(this));var g=i.stack;this.componentDidCatch(i.value,{componentStack:g!==null?g:""})})}function xv(t,e,n,i,s){if(n.flags|=32768,i!==null&&typeof i=="object"&&typeof i.then=="function"){if(e=n.alternate,e!==null&&Mi(e,n,s,!0),n=Re.current,n!==null){switch(n.tag){case 13:return Ge===null?kr():n.alternate===null&&Vt===0&&(Vt=3),n.flags&=-257,n.flags|=65536,n.lanes=s,i===$u?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([i]):e.add(i),Jr(t,i,s)),!1;case 22:return n.flags|=65536,i===$u?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([i])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([i]):n.add(i)),Jr(t,i,s)),!1}throw Error(r(435,n.tag))}return Jr(t,i,s),kr(),!1}if(pt)return e=Re.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=s,i!==Qu&&(t=Error(r(422),{cause:i}),Ei(Ae(t,n)))):(i!==Qu&&(e=Error(r(423),{cause:i}),Ei(Ae(e,n))),t=t.current.alternate,t.flags|=65536,s&=-s,t.lanes|=s,i=Ae(i,n),s=Ar(t.stateNode,i,s),er(t,s),Vt!==4&&(Vt=2)),!1;var o=Error(r(520),{cause:i});if(o=Ae(o,n),Qi===null?Qi=[o]:Qi.push(o),Vt!==4&&(Vt=2),e===null)return!0;i=Ae(i,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=s&-s,n.lanes|=t,t=Ar(n.stateNode,i,t),er(n,t),!1;case 1:if(e=n.type,o=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||o!==null&&typeof o.componentDidCatch=="function"&&(_n===null||!_n.has(o))))return n.flags|=65536,s&=-s,n.lanes|=s,s=ud(s),rd(s,t,n,i),er(n,s),!1}n=n.return}while(n!==null);return!1}var od=Error(r(461)),Qt=!1;function Jt(t,e,n,i){e.child=t===null?Ih(e,null,n,i):La(e,t.child,n,i)}function cd(t,e,n,i,s){n=n.render;var o=e.ref;if("ref"in i){var h={};for(var g in i)g!=="ref"&&(h[g]=i[g])}else h=i;return ta(e),i=sr(t,e,n,h,o,s),g=ur(),t!==null&&!Qt?(rr(t,e,s),sn(t,e,s)):(pt&&g&&Xu(e),e.flags|=1,Jt(t,e,i,s),e.child)}function fd(t,e,n,i,s){if(t===null){var o=n.type;return typeof o=="function"&&!qu(o)&&o.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=o,hd(t,e,o,i,s)):(t=Ql(n.type,null,i,e,e.mode,s),t.ref=e.ref,t.return=e,e.child=t)}if(o=t.child,!Nr(t,s)){var h=o.memoizedProps;if(n=n.compare,n=n!==null?n:Si,n(h,i)&&t.ref===e.ref)return sn(t,e,s)}return e.flags|=1,t=$e(o,i),t.ref=e.ref,t.return=e,e.child=t}function hd(t,e,n,i,s){if(t!==null){var o=t.memoizedProps;if(Si(o,i)&&t.ref===e.ref)if(Qt=!1,e.pendingProps=i=o,Nr(t,s))(t.flags&131072)!==0&&(Qt=!0);else return e.lanes=t.lanes,sn(t,e,s)}return Er(t,e,n,i,s)}function dd(t,e,n){var i=e.pendingProps,s=i.children,o=t!==null?t.memoizedState:null;if(i.mode==="hidden"){if((e.flags&128)!==0){if(i=o!==null?o.baseLanes|n:n,t!==null){for(s=e.child=t.child,o=0;s!==null;)o=o|s.lanes|s.childLanes,s=s.sibling;e.childLanes=o&~i}else e.childLanes=0,e.child=null;return md(t,e,i,n)}if((n&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Fl(e,o!==null?o.cachePool:null),o!==null?hh(e,o):ar(),td(e);else return e.lanes=e.childLanes=536870912,md(t,e,o!==null?o.baseLanes|n:n,n)}else o!==null?(Fl(e,o.cachePool),hh(e,o),En(),e.memoizedState=null):(t!==null&&Fl(e,null),ar(),En());return Jt(t,e,s,n),e.child}function md(t,e,n,i){var s=Wu();return s=s===null?null:{parent:Yt._currentValue,pool:s},e.memoizedState={baseLanes:n,cachePool:s},t!==null&&Fl(e,null),ar(),td(e),t!==null&&Mi(t,e,i,!0),null}function ds(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(r(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function Er(t,e,n,i,s){return ta(e),n=sr(t,e,n,i,void 0,s),i=ur(),t!==null&&!Qt?(rr(t,e,s),sn(t,e,s)):(pt&&i&&Xu(e),e.flags|=1,Jt(t,e,n,s),e.child)}function pd(t,e,n,i,s,o){return ta(e),e.updateQueue=null,n=mh(e,i,n,s),dh(t),i=ur(),t!==null&&!Qt?(rr(t,e,o),sn(t,e,o)):(pt&&i&&Xu(e),e.flags|=1,Jt(t,e,n,o),e.child)}function yd(t,e,n,i,s){if(ta(e),e.stateNode===null){var o=Oa,h=n.contextType;typeof h=="object"&&h!==null&&(o=te(h)),o=new n(i,o),e.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,o.updater=xr,e.stateNode=o,o._reactInternals=e,o=e.stateNode,o.props=i,o.state=e.memoizedState,o.refs={},Iu(e),h=n.contextType,o.context=typeof h=="object"&&h!==null?te(h):Oa,o.state=e.memoizedState,h=n.getDerivedStateFromProps,typeof h=="function"&&(Tr(e,n,h,i),o.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(h=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),h!==o.state&&xr.enqueueReplaceState(o,o.state,null),Vi(e,i,o,s),Ni(),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308),i=!0}else if(t===null){o=e.stateNode;var g=e.memoizedProps,S=aa(n,g);o.props=S;var D=o.context,N=n.contextType;h=Oa,typeof N=="object"&&N!==null&&(h=te(N));var w=n.getDerivedStateFromProps;N=typeof w=="function"||typeof o.getSnapshotBeforeUpdate=="function",g=e.pendingProps!==g,N||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(g||D!==h)&&nd(e,o,i,h),bn=!1;var R=e.memoizedState;o.state=R,Vi(e,i,o,s),Ni(),D=e.memoizedState,g||R!==D||bn?(typeof w=="function"&&(Tr(e,n,w,i),D=e.memoizedState),(S=bn||ed(e,n,S,i,R,D,h))?(N||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(e.flags|=4194308)):(typeof o.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=i,e.memoizedState=D),o.props=i,o.state=D,o.context=h,i=S):(typeof o.componentDidMount=="function"&&(e.flags|=4194308),i=!1)}else{o=e.stateNode,tr(t,e),h=e.memoizedProps,N=aa(n,h),o.props=N,w=e.pendingProps,R=o.context,D=n.contextType,S=Oa,typeof D=="object"&&D!==null&&(S=te(D)),g=n.getDerivedStateFromProps,(D=typeof g=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(h!==w||R!==S)&&nd(e,o,i,S),bn=!1,R=e.memoizedState,o.state=R,Vi(e,i,o,s),Ni();var O=e.memoizedState;h!==w||R!==O||bn||t!==null&&t.dependencies!==null&&Pl(t.dependencies)?(typeof g=="function"&&(Tr(e,n,g,i),O=e.memoizedState),(N=bn||ed(e,n,N,i,R,O,S)||t!==null&&t.dependencies!==null&&Pl(t.dependencies))?(D||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(i,O,S),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(i,O,S)),typeof o.componentDidUpdate=="function"&&(e.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof o.componentDidUpdate!="function"||h===t.memoizedProps&&R===t.memoizedState||(e.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||h===t.memoizedProps&&R===t.memoizedState||(e.flags|=1024),e.memoizedProps=i,e.memoizedState=O),o.props=i,o.state=O,o.context=S,i=N):(typeof o.componentDidUpdate!="function"||h===t.memoizedProps&&R===t.memoizedState||(e.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||h===t.memoizedProps&&R===t.memoizedState||(e.flags|=1024),i=!1)}return o=i,ds(t,e),i=(e.flags&128)!==0,o||i?(o=e.stateNode,n=i&&typeof n.getDerivedStateFromError!="function"?null:o.render(),e.flags|=1,t!==null&&i?(e.child=La(e,t.child,null,s),e.child=La(e,null,n,s)):Jt(t,e,n,s),e.memoizedState=o.state,t=e.child):t=sn(t,e,s),t}function gd(t,e,n,i){return Ai(),e.flags|=256,Jt(t,e,n,i),e.child}var Mr={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Dr(t){return{baseLanes:t,cachePool:ih()}}function Rr(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=Oe),t}function vd(t,e,n){var i=e.pendingProps,s=!1,o=(e.flags&128)!==0,h;if((h=o)||(h=t!==null&&t.memoizedState===null?!1:(Gt.current&2)!==0),h&&(s=!0,e.flags&=-129),h=(e.flags&32)!==0,e.flags&=-33,t===null){if(pt){if(s?An(e):En(),pt){var g=Nt,S;if(S=g){t:{for(S=g,g=Ye;S.nodeType!==8;){if(!g){g=null;break t}if(S=je(S.nextSibling),S===null){g=null;break t}}g=S}g!==null?(e.memoizedState={dehydrated:g,treeContext:Jn!==null?{id:Ie,overflow:tn}:null,retryLane:536870912,hydrationErrors:null},S=pe(18,null,null,0),S.stateNode=g,S.return=e,e.child=S,ae=e,Nt=null,S=!0):S=!1}S||$n(e)}if(g=e.memoizedState,g!==null&&(g=g.dehydrated,g!==null))return co(g)?e.lanes=32:e.lanes=536870912,null;ln(e)}return g=i.children,i=i.fallback,s?(En(),s=e.mode,g=ms({mode:"hidden",children:g},s),i=Pn(i,s,n,null),g.return=e,i.return=e,g.sibling=i,e.child=g,s=e.child,s.memoizedState=Dr(n),s.childLanes=Rr(t,h,n),e.memoizedState=Mr,i):(An(e),Or(e,g))}if(S=t.memoizedState,S!==null&&(g=S.dehydrated,g!==null)){if(o)e.flags&256?(An(e),e.flags&=-257,e=Cr(t,e,n)):e.memoizedState!==null?(En(),e.child=t.child,e.flags|=128,e=null):(En(),s=i.fallback,g=e.mode,i=ms({mode:"visible",children:i.children},g),s=Pn(s,g,n,null),s.flags|=2,i.return=e,s.return=e,i.sibling=s,e.child=i,La(e,t.child,null,n),i=e.child,i.memoizedState=Dr(n),i.childLanes=Rr(t,h,n),e.memoizedState=Mr,e=s);else if(An(e),co(g)){if(h=g.nextSibling&&g.nextSibling.dataset,h)var D=h.dgst;h=D,i=Error(r(419)),i.stack="",i.digest=h,Ei({value:i,source:null,stack:null}),e=Cr(t,e,n)}else if(Qt||Mi(t,e,n,!1),h=(n&t.childLanes)!==0,Qt||h){if(h=At,h!==null&&(i=n&-n,i=(i&42)!==0?1:fu(i),i=(i&(h.suspendedLanes|n))!==0?0:i,i!==0&&i!==S.retryLane))throw S.retryLane=i,Ra(t,i),Se(h,t,i),od;g.data==="$?"||kr(),e=Cr(t,e,n)}else g.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=S.treeContext,Nt=je(g.nextSibling),ae=e,pt=!0,Wn=null,Ye=!1,t!==null&&(Me[De++]=Ie,Me[De++]=tn,Me[De++]=Jn,Ie=t.id,tn=t.overflow,Jn=e),e=Or(e,i.children),e.flags|=4096);return e}return s?(En(),s=i.fallback,g=e.mode,S=t.child,D=S.sibling,i=$e(S,{mode:"hidden",children:i.children}),i.subtreeFlags=S.subtreeFlags&65011712,D!==null?s=$e(D,s):(s=Pn(s,g,n,null),s.flags|=2),s.return=e,i.return=e,i.sibling=s,e.child=i,i=s,s=e.child,g=t.child.memoizedState,g===null?g=Dr(n):(S=g.cachePool,S!==null?(D=Yt._currentValue,S=S.parent!==D?{parent:D,pool:D}:S):S=ih(),g={baseLanes:g.baseLanes|n,cachePool:S}),s.memoizedState=g,s.childLanes=Rr(t,h,n),e.memoizedState=Mr,i):(An(e),n=t.child,t=n.sibling,n=$e(n,{mode:"visible",children:i.children}),n.return=e,n.sibling=null,t!==null&&(h=e.deletions,h===null?(e.deletions=[t],e.flags|=16):h.push(t)),e.child=n,e.memoizedState=null,n)}function Or(t,e){return e=ms({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function ms(t,e){return t=pe(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Cr(t,e,n){return La(e,t.child,null,n),t=Or(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function bd(t,e,n){t.lanes|=e;var i=t.alternate;i!==null&&(i.lanes|=e),ku(t.return,e,n)}function _r(t,e,n,i,s){var o=t.memoizedState;o===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:i,tail:n,tailMode:s}:(o.isBackwards=e,o.rendering=null,o.renderingStartTime=0,o.last=i,o.tail=n,o.tailMode=s)}function Sd(t,e,n){var i=e.pendingProps,s=i.revealOrder,o=i.tail;if(Jt(t,e,i.children,n),i=Gt.current,(i&2)!==0)i=i&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&bd(t,n,e);else if(t.tag===19)bd(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}i&=1}switch(X(Gt,i),s){case"forwards":for(n=e.child,s=null;n!==null;)t=n.alternate,t!==null&&cs(t)===null&&(s=n),n=n.sibling;n=s,n===null?(s=e.child,e.child=null):(s=n.sibling,n.sibling=null),_r(e,!1,s,n,o);break;case"backwards":for(n=null,s=e.child,e.child=null;s!==null;){if(t=s.alternate,t!==null&&cs(t)===null){e.child=s;break}t=s.sibling,s.sibling=n,n=s,s=t}_r(e,!0,n,null,o);break;case"together":_r(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function sn(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),Cn|=e.lanes,(n&e.childLanes)===0)if(t!==null){if(Mi(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(r(153));if(e.child!==null){for(t=e.child,n=$e(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=$e(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function Nr(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Pl(t)))}function Av(t,e,n){switch(e.tag){case 3:Mt(e,e.stateNode.containerInfo),vn(e,Yt,t.memoizedState.cache),Ai();break;case 27:case 5:su(e);break;case 4:Mt(e,e.stateNode.containerInfo);break;case 10:vn(e,e.type,e.memoizedProps.value);break;case 13:var i=e.memoizedState;if(i!==null)return i.dehydrated!==null?(An(e),e.flags|=128,null):(n&e.child.childLanes)!==0?vd(t,e,n):(An(e),t=sn(t,e,n),t!==null?t.sibling:null);An(e);break;case 19:var s=(t.flags&128)!==0;if(i=(n&e.childLanes)!==0,i||(Mi(t,e,n,!1),i=(n&e.childLanes)!==0),s){if(i)return Sd(t,e,n);e.flags|=128}if(s=e.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),X(Gt,Gt.current),i)break;return null;case 22:case 23:return e.lanes=0,dd(t,e,n);case 24:vn(e,Yt,t.memoizedState.cache)}return sn(t,e,n)}function Td(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)Qt=!0;else{if(!Nr(t,n)&&(e.flags&128)===0)return Qt=!1,Av(t,e,n);Qt=(t.flags&131072)!==0}else Qt=!1,pt&&(e.flags&1048576)!==0&&Wf(e,kl,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var i=e.elementType,s=i._init;if(i=s(i._payload),e.type=i,typeof i=="function")qu(i)?(t=aa(i,t),e.tag=1,e=yd(null,e,i,t,n)):(e.tag=0,e=Er(null,e,i,t,n));else{if(i!=null){if(s=i.$$typeof,s===tt){e.tag=11,e=cd(null,e,i,t,n);break t}else if(s===nt){e.tag=14,e=fd(null,e,i,t,n);break t}}throw e=He(i)||i,Error(r(306,e,""))}}return e;case 0:return Er(t,e,e.type,e.pendingProps,n);case 1:return i=e.type,s=aa(i,e.pendingProps),yd(t,e,i,s,n);case 3:t:{if(Mt(e,e.stateNode.containerInfo),t===null)throw Error(r(387));i=e.pendingProps;var o=e.memoizedState;s=o.element,tr(t,e),Vi(e,i,null,n);var h=e.memoizedState;if(i=h.cache,vn(e,Yt,i),i!==o.cache&&Pu(e,[Yt],n,!0),Ni(),i=h.element,o.isDehydrated)if(o={element:i,isDehydrated:!1,cache:h.cache},e.updateQueue.baseState=o,e.memoizedState=o,e.flags&256){e=gd(t,e,i,n);break t}else if(i!==s){s=Ae(Error(r(424)),e),Ei(s),e=gd(t,e,i,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Nt=je(t.firstChild),ae=e,pt=!0,Wn=null,Ye=!0,n=Ih(e,null,i,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Ai(),i===s){e=sn(t,e,n);break t}Jt(t,e,i,n)}e=e.child}return e;case 26:return ds(t,e),t===null?(n=Mm(e.type,null,e.pendingProps,null))?e.memoizedState=n:pt||(n=e.type,t=e.pendingProps,i=Os(lt.current).createElement(n),i[It]=e,i[le]=t,Wt(i,n,t),Zt(i),e.stateNode=i):e.memoizedState=Mm(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return su(e),t===null&&pt&&(i=e.stateNode=xm(e.type,e.pendingProps,lt.current),ae=e,Ye=!0,s=Nt,zn(e.type)?(fo=s,Nt=je(i.firstChild)):Nt=s),Jt(t,e,e.pendingProps.children,n),ds(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&pt&&((s=i=Nt)&&(i=Wv(i,e.type,e.pendingProps,Ye),i!==null?(e.stateNode=i,ae=e,Nt=je(i.firstChild),Ye=!1,s=!0):s=!1),s||$n(e)),su(e),s=e.type,o=e.pendingProps,h=t!==null?t.memoizedProps:null,i=o.children,uo(s,o)?i=null:h!==null&&uo(s,h)&&(e.flags|=32),e.memoizedState!==null&&(s=sr(t,e,pv,null,null,n),tl._currentValue=s),ds(t,e),Jt(t,e,i,n),e.child;case 6:return t===null&&pt&&((t=n=Nt)&&(n=$v(n,e.pendingProps,Ye),n!==null?(e.stateNode=n,ae=e,Nt=null,t=!0):t=!1),t||$n(e)),null;case 13:return vd(t,e,n);case 4:return Mt(e,e.stateNode.containerInfo),i=e.pendingProps,t===null?e.child=La(e,null,i,n):Jt(t,e,i,n),e.child;case 11:return cd(t,e,e.type,e.pendingProps,n);case 7:return Jt(t,e,e.pendingProps,n),e.child;case 8:return Jt(t,e,e.pendingProps.children,n),e.child;case 12:return Jt(t,e,e.pendingProps.children,n),e.child;case 10:return i=e.pendingProps,vn(e,e.type,i.value),Jt(t,e,i.children,n),e.child;case 9:return s=e.type._context,i=e.pendingProps.children,ta(e),s=te(s),i=i(s),e.flags|=1,Jt(t,e,i,n),e.child;case 14:return fd(t,e,e.type,e.pendingProps,n);case 15:return hd(t,e,e.type,e.pendingProps,n);case 19:return Sd(t,e,n);case 31:return i=e.pendingProps,n=e.mode,i={mode:i.mode,children:i.children},t===null?(n=ms(i,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=$e(t.child,i),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return dd(t,e,n);case 24:return ta(e),i=te(Yt),t===null?(s=Wu(),s===null&&(s=At,o=Ju(),s.pooledCache=o,o.refCount++,o!==null&&(s.pooledCacheLanes|=n),s=o),e.memoizedState={parent:i,cache:s},Iu(e),vn(e,Yt,s)):((t.lanes&n)!==0&&(tr(t,e),Vi(e,null,null,n),Ni()),s=t.memoizedState,o=e.memoizedState,s.parent!==i?(s={parent:i,cache:i},e.memoizedState=s,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=s),vn(e,Yt,i)):(i=o.cache,vn(e,Yt,i),i!==s.cache&&Pu(e,[Yt],n,!0))),Jt(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(r(156,e.tag))}function un(t){t.flags|=4}function xd(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!_m(e)){if(e=Re.current,e!==null&&((ft&4194048)===ft?Ge!==null:(ft&62914560)!==ft&&(ft&536870912)===0||e!==Ge))throw Ci=$u,lh;t.flags|=8192}}function ps(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Ic():536870912,t.lanes|=e,Ga|=e)}function Hi(t,e){if(!pt)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:i.sibling=null}}function Ot(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,i=0;if(e)for(var s=t.child;s!==null;)n|=s.lanes|s.childLanes,i|=s.subtreeFlags&65011712,i|=s.flags&65011712,s.return=t,s=s.sibling;else for(s=t.child;s!==null;)n|=s.lanes|s.childLanes,i|=s.subtreeFlags,i|=s.flags,s.return=t,s=s.sibling;return t.subtreeFlags|=i,t.childLanes=n,e}function Ev(t,e,n){var i=e.pendingProps;switch(Zu(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ot(e),null;case 1:return Ot(e),null;case 3:return n=e.stateNode,i=null,t!==null&&(i=t.memoizedState.cache),e.memoizedState.cache!==i&&(e.flags|=2048),nn(Yt),mn(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(xi(e)?un(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,th())),Ot(e),null;case 26:return n=e.memoizedState,t===null?(un(e),n!==null?(Ot(e),xd(e,n)):(Ot(e),e.flags&=-16777217)):n?n!==t.memoizedState?(un(e),Ot(e),xd(e,n)):(Ot(e),e.flags&=-16777217):(t.memoizedProps!==i&&un(e),Ot(e),e.flags&=-16777217),null;case 27:Dl(e),n=lt.current;var s=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==i&&un(e);else{if(!i){if(e.stateNode===null)throw Error(r(166));return Ot(e),null}t=$.current,xi(e)?$f(e):(t=xm(s,i,n),e.stateNode=t,un(e))}return Ot(e),null;case 5:if(Dl(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==i&&un(e);else{if(!i){if(e.stateNode===null)throw Error(r(166));return Ot(e),null}if(t=$.current,xi(e))$f(e);else{switch(s=Os(lt.current),t){case 1:t=s.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=s.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=s.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=s.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=s.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof i.is=="string"?s.createElement("select",{is:i.is}):s.createElement("select"),i.multiple?t.multiple=!0:i.size&&(t.size=i.size);break;default:t=typeof i.is=="string"?s.createElement(n,{is:i.is}):s.createElement(n)}}t[It]=e,t[le]=i;t:for(s=e.child;s!==null;){if(s.tag===5||s.tag===6)t.appendChild(s.stateNode);else if(s.tag!==4&&s.tag!==27&&s.child!==null){s.child.return=s,s=s.child;continue}if(s===e)break t;for(;s.sibling===null;){if(s.return===null||s.return===e)break t;s=s.return}s.sibling.return=s.return,s=s.sibling}e.stateNode=t;t:switch(Wt(t,n,i),n){case"button":case"input":case"select":case"textarea":t=!!i.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&un(e)}}return Ot(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==i&&un(e);else{if(typeof i!="string"&&e.stateNode===null)throw Error(r(166));if(t=lt.current,xi(e)){if(t=e.stateNode,n=e.memoizedProps,i=null,s=ae,s!==null)switch(s.tag){case 27:case 5:i=s.memoizedProps}t[It]=e,t=!!(t.nodeValue===n||i!==null&&i.suppressHydrationWarning===!0||pm(t.nodeValue,n)),t||$n(e)}else t=Os(t).createTextNode(i),t[It]=e,e.stateNode=t}return Ot(e),null;case 13:if(i=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(s=xi(e),i!==null&&i.dehydrated!==null){if(t===null){if(!s)throw Error(r(318));if(s=e.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(r(317));s[It]=e}else Ai(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Ot(e),s=!1}else s=th(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=s),s=!0;if(!s)return e.flags&256?(ln(e),e):(ln(e),null)}if(ln(e),(e.flags&128)!==0)return e.lanes=n,e;if(n=i!==null,t=t!==null&&t.memoizedState!==null,n){i=e.child,s=null,i.alternate!==null&&i.alternate.memoizedState!==null&&i.alternate.memoizedState.cachePool!==null&&(s=i.alternate.memoizedState.cachePool.pool);var o=null;i.memoizedState!==null&&i.memoizedState.cachePool!==null&&(o=i.memoizedState.cachePool.pool),o!==s&&(i.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),ps(e,e.updateQueue),Ot(e),null;case 4:return mn(),t===null&&no(e.stateNode.containerInfo),Ot(e),null;case 10:return nn(e.type),Ot(e),null;case 19:if(K(Gt),s=e.memoizedState,s===null)return Ot(e),null;if(i=(e.flags&128)!==0,o=s.rendering,o===null)if(i)Hi(s,!1);else{if(Vt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(o=cs(t),o!==null){for(e.flags|=128,Hi(s,!1),t=o.updateQueue,e.updateQueue=t,ps(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)Ff(n,t),n=n.sibling;return X(Gt,Gt.current&1|2),e.child}t=t.sibling}s.tail!==null&&qe()>vs&&(e.flags|=128,i=!0,Hi(s,!1),e.lanes=4194304)}else{if(!i)if(t=cs(o),t!==null){if(e.flags|=128,i=!0,t=t.updateQueue,e.updateQueue=t,ps(e,t),Hi(s,!0),s.tail===null&&s.tailMode==="hidden"&&!o.alternate&&!pt)return Ot(e),null}else 2*qe()-s.renderingStartTime>vs&&n!==536870912&&(e.flags|=128,i=!0,Hi(s,!1),e.lanes=4194304);s.isBackwards?(o.sibling=e.child,e.child=o):(t=s.last,t!==null?t.sibling=o:e.child=o,s.last=o)}return s.tail!==null?(e=s.tail,s.rendering=e,s.tail=e.sibling,s.renderingStartTime=qe(),e.sibling=null,t=Gt.current,X(Gt,i?t&1|2:t&1),e):(Ot(e),null);case 22:case 23:return ln(e),ir(),i=e.memoizedState!==null,t!==null?t.memoizedState!==null!==i&&(e.flags|=8192):i&&(e.flags|=8192),i?(n&536870912)!==0&&(e.flags&128)===0&&(Ot(e),e.subtreeFlags&6&&(e.flags|=8192)):Ot(e),n=e.updateQueue,n!==null&&ps(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),i=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(i=e.memoizedState.cachePool.pool),i!==n&&(e.flags|=2048),t!==null&&K(ea),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),nn(Yt),Ot(e),null;case 25:return null;case 30:return null}throw Error(r(156,e.tag))}function Mv(t,e){switch(Zu(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return nn(Yt),mn(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return Dl(e),null;case 13:if(ln(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(r(340));Ai()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return K(Gt),null;case 4:return mn(),null;case 10:return nn(e.type),null;case 22:case 23:return ln(e),ir(),t!==null&&K(ea),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return nn(Yt),null;case 25:return null;default:return null}}function Ad(t,e){switch(Zu(e),e.tag){case 3:nn(Yt),mn();break;case 26:case 27:case 5:Dl(e);break;case 4:mn();break;case 13:ln(e);break;case 19:K(Gt);break;case 10:nn(e.type);break;case 22:case 23:ln(e),ir(),t!==null&&K(ea);break;case 24:nn(Yt)}}function qi(t,e){try{var n=e.updateQueue,i=n!==null?n.lastEffect:null;if(i!==null){var s=i.next;n=s;do{if((n.tag&t)===t){i=void 0;var o=n.create,h=n.inst;i=o(),h.destroy=i}n=n.next}while(n!==s)}}catch(g){xt(e,e.return,g)}}function Mn(t,e,n){try{var i=e.updateQueue,s=i!==null?i.lastEffect:null;if(s!==null){var o=s.next;i=o;do{if((i.tag&t)===t){var h=i.inst,g=h.destroy;if(g!==void 0){h.destroy=void 0,s=e;var S=n,D=g;try{D()}catch(N){xt(s,S,N)}}}i=i.next}while(i!==o)}}catch(N){xt(e,e.return,N)}}function Ed(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{fh(e,n)}catch(i){xt(t,t.return,i)}}}function Md(t,e,n){n.props=aa(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(i){xt(t,e,i)}}function Yi(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var i=t.stateNode;break;case 30:i=t.stateNode;break;default:i=t.stateNode}typeof n=="function"?t.refCleanup=n(i):n.current=i}}catch(s){xt(t,e,s)}}function Xe(t,e){var n=t.ref,i=t.refCleanup;if(n!==null)if(typeof i=="function")try{i()}catch(s){xt(t,e,s)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(s){xt(t,e,s)}else n.current=null}function Dd(t){var e=t.type,n=t.memoizedProps,i=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&i.focus();break t;case"img":n.src?i.src=n.src:n.srcSet&&(i.srcset=n.srcSet)}}catch(s){xt(t,t.return,s)}}function Vr(t,e,n){try{var i=t.stateNode;Kv(i,t.type,n,e),i[le]=e}catch(s){xt(t,t.return,s)}}function Rd(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&zn(t.type)||t.tag===4}function zr(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Rd(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&zn(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function wr(t,e,n){var i=t.tag;if(i===5||i===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=Rs));else if(i!==4&&(i===27&&zn(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(wr(t,e,n),t=t.sibling;t!==null;)wr(t,e,n),t=t.sibling}function ys(t,e,n){var i=t.tag;if(i===5||i===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(i!==4&&(i===27&&zn(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(ys(t,e,n),t=t.sibling;t!==null;)ys(t,e,n),t=t.sibling}function Od(t){var e=t.stateNode,n=t.memoizedProps;try{for(var i=t.type,s=e.attributes;s.length;)e.removeAttributeNode(s[0]);Wt(e,i,n),e[It]=t,e[le]=n}catch(o){xt(t,t.return,o)}}var rn=!1,jt=!1,jr=!1,Cd=typeof WeakSet=="function"?WeakSet:Set,Kt=null;function Dv(t,e){if(t=t.containerInfo,lo=ws,t=qf(t),zu(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var i=n.getSelection&&n.getSelection();if(i&&i.rangeCount!==0){n=i.anchorNode;var s=i.anchorOffset,o=i.focusNode;i=i.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break t}var h=0,g=-1,S=-1,D=0,N=0,w=t,R=null;e:for(;;){for(var O;w!==n||s!==0&&w.nodeType!==3||(g=h+s),w!==o||i!==0&&w.nodeType!==3||(S=h+i),w.nodeType===3&&(h+=w.nodeValue.length),(O=w.firstChild)!==null;)R=w,w=O;for(;;){if(w===t)break e;if(R===n&&++D===s&&(g=h),R===o&&++N===i&&(S=h),(O=w.nextSibling)!==null)break;w=R,R=w.parentNode}w=O}n=g===-1||S===-1?null:{start:g,end:S}}else n=null}n=n||{start:0,end:0}}else n=null;for(so={focusedElem:t,selectionRange:n},ws=!1,Kt=e;Kt!==null;)if(e=Kt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Kt=t;else for(;Kt!==null;){switch(e=Kt,o=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&o!==null){t=void 0,n=e,s=o.memoizedProps,o=o.memoizedState,i=n.stateNode;try{var at=aa(n.type,s,n.elementType===n.type);t=i.getSnapshotBeforeUpdate(at,o),i.__reactInternalSnapshotBeforeUpdate=t}catch(I){xt(n,n.return,I)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)oo(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":oo(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(r(163))}if(t=e.sibling,t!==null){t.return=e.return,Kt=t;break}Kt=e.return}}function _d(t,e,n){var i=n.flags;switch(n.tag){case 0:case 11:case 15:Dn(t,n),i&4&&qi(5,n);break;case 1:if(Dn(t,n),i&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(h){xt(n,n.return,h)}else{var s=aa(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(s,e,t.__reactInternalSnapshotBeforeUpdate)}catch(h){xt(n,n.return,h)}}i&64&&Ed(n),i&512&&Yi(n,n.return);break;case 3:if(Dn(t,n),i&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{fh(t,e)}catch(h){xt(n,n.return,h)}}break;case 27:e===null&&i&4&&Od(n);case 26:case 5:Dn(t,n),e===null&&i&4&&Dd(n),i&512&&Yi(n,n.return);break;case 12:Dn(t,n);break;case 13:Dn(t,n),i&4&&zd(t,n),i&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=jv.bind(null,n),Iv(t,n))));break;case 22:if(i=n.memoizedState!==null||rn,!i){e=e!==null&&e.memoizedState!==null||jt,s=rn;var o=jt;rn=i,(jt=e)&&!o?Rn(t,n,(n.subtreeFlags&8772)!==0):Dn(t,n),rn=s,jt=o}break;case 30:break;default:Dn(t,n)}}function Nd(t){var e=t.alternate;e!==null&&(t.alternate=null,Nd(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&mu(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Dt=null,re=!1;function on(t,e,n){for(n=n.child;n!==null;)Vd(t,e,n),n=n.sibling}function Vd(t,e,n){if(he&&typeof he.onCommitFiberUnmount=="function")try{he.onCommitFiberUnmount(ri,n)}catch{}switch(n.tag){case 26:jt||Xe(n,e),on(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:jt||Xe(n,e);var i=Dt,s=re;zn(n.type)&&(Dt=n.stateNode,re=!1),on(t,e,n),Fi(n.stateNode),Dt=i,re=s;break;case 5:jt||Xe(n,e);case 6:if(i=Dt,s=re,Dt=null,on(t,e,n),Dt=i,re=s,Dt!==null)if(re)try{(Dt.nodeType===9?Dt.body:Dt.nodeName==="HTML"?Dt.ownerDocument.body:Dt).removeChild(n.stateNode)}catch(o){xt(n,e,o)}else try{Dt.removeChild(n.stateNode)}catch(o){xt(n,e,o)}break;case 18:Dt!==null&&(re?(t=Dt,Sm(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),il(t)):Sm(Dt,n.stateNode));break;case 4:i=Dt,s=re,Dt=n.stateNode.containerInfo,re=!0,on(t,e,n),Dt=i,re=s;break;case 0:case 11:case 14:case 15:jt||Mn(2,n,e),jt||Mn(4,n,e),on(t,e,n);break;case 1:jt||(Xe(n,e),i=n.stateNode,typeof i.componentWillUnmount=="function"&&Md(n,e,i)),on(t,e,n);break;case 21:on(t,e,n);break;case 22:jt=(i=jt)||n.memoizedState!==null,on(t,e,n),jt=i;break;default:on(t,e,n)}}function zd(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{il(t)}catch(n){xt(e,e.return,n)}}function Rv(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Cd),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Cd),e;default:throw Error(r(435,t.tag))}}function Ur(t,e){var n=Rv(t);e.forEach(function(i){var s=Uv.bind(null,t,i);n.has(i)||(n.add(i),i.then(s,s))})}function ye(t,e){var n=e.deletions;if(n!==null)for(var i=0;i<n.length;i++){var s=n[i],o=t,h=e,g=h;t:for(;g!==null;){switch(g.tag){case 27:if(zn(g.type)){Dt=g.stateNode,re=!1;break t}break;case 5:Dt=g.stateNode,re=!1;break t;case 3:case 4:Dt=g.stateNode.containerInfo,re=!0;break t}g=g.return}if(Dt===null)throw Error(r(160));Vd(o,h,s),Dt=null,re=!1,o=s.alternate,o!==null&&(o.return=null),s.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)wd(e,t),e=e.sibling}var we=null;function wd(t,e){var n=t.alternate,i=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:ye(e,t),ge(t),i&4&&(Mn(3,t,t.return),qi(3,t),Mn(5,t,t.return));break;case 1:ye(e,t),ge(t),i&512&&(jt||n===null||Xe(n,n.return)),i&64&&rn&&(t=t.updateQueue,t!==null&&(i=t.callbacks,i!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?i:n.concat(i))));break;case 26:var s=we;if(ye(e,t),ge(t),i&512&&(jt||n===null||Xe(n,n.return)),i&4){var o=n!==null?n.memoizedState:null;if(i=t.memoizedState,n===null)if(i===null)if(t.stateNode===null){t:{i=t.type,n=t.memoizedProps,s=s.ownerDocument||s;e:switch(i){case"title":o=s.getElementsByTagName("title")[0],(!o||o[fi]||o[It]||o.namespaceURI==="http://www.w3.org/2000/svg"||o.hasAttribute("itemprop"))&&(o=s.createElement(i),s.head.insertBefore(o,s.querySelector("head > title"))),Wt(o,i,n),o[It]=t,Zt(o),i=o;break t;case"link":var h=Om("link","href",s).get(i+(n.href||""));if(h){for(var g=0;g<h.length;g++)if(o=h[g],o.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&o.getAttribute("rel")===(n.rel==null?null:n.rel)&&o.getAttribute("title")===(n.title==null?null:n.title)&&o.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){h.splice(g,1);break e}}o=s.createElement(i),Wt(o,i,n),s.head.appendChild(o);break;case"meta":if(h=Om("meta","content",s).get(i+(n.content||""))){for(g=0;g<h.length;g++)if(o=h[g],o.getAttribute("content")===(n.content==null?null:""+n.content)&&o.getAttribute("name")===(n.name==null?null:n.name)&&o.getAttribute("property")===(n.property==null?null:n.property)&&o.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&o.getAttribute("charset")===(n.charSet==null?null:n.charSet)){h.splice(g,1);break e}}o=s.createElement(i),Wt(o,i,n),s.head.appendChild(o);break;default:throw Error(r(468,i))}o[It]=t,Zt(o),i=o}t.stateNode=i}else Cm(s,t.type,t.stateNode);else t.stateNode=Rm(s,i,t.memoizedProps);else o!==i?(o===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):o.count--,i===null?Cm(s,t.type,t.stateNode):Rm(s,i,t.memoizedProps)):i===null&&t.stateNode!==null&&Vr(t,t.memoizedProps,n.memoizedProps)}break;case 27:ye(e,t),ge(t),i&512&&(jt||n===null||Xe(n,n.return)),n!==null&&i&4&&Vr(t,t.memoizedProps,n.memoizedProps);break;case 5:if(ye(e,t),ge(t),i&512&&(jt||n===null||Xe(n,n.return)),t.flags&32){s=t.stateNode;try{Sa(s,"")}catch(O){xt(t,t.return,O)}}i&4&&t.stateNode!=null&&(s=t.memoizedProps,Vr(t,s,n!==null?n.memoizedProps:s)),i&1024&&(jr=!0);break;case 6:if(ye(e,t),ge(t),i&4){if(t.stateNode===null)throw Error(r(162));i=t.memoizedProps,n=t.stateNode;try{n.nodeValue=i}catch(O){xt(t,t.return,O)}}break;case 3:if(Ns=null,s=we,we=Cs(e.containerInfo),ye(e,t),we=s,ge(t),i&4&&n!==null&&n.memoizedState.isDehydrated)try{il(e.containerInfo)}catch(O){xt(t,t.return,O)}jr&&(jr=!1,jd(t));break;case 4:i=we,we=Cs(t.stateNode.containerInfo),ye(e,t),ge(t),we=i;break;case 12:ye(e,t),ge(t);break;case 13:ye(e,t),ge(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Gr=qe()),i&4&&(i=t.updateQueue,i!==null&&(t.updateQueue=null,Ur(t,i)));break;case 22:s=t.memoizedState!==null;var S=n!==null&&n.memoizedState!==null,D=rn,N=jt;if(rn=D||s,jt=N||S,ye(e,t),jt=N,rn=D,ge(t),i&8192)t:for(e=t.stateNode,e._visibility=s?e._visibility&-2:e._visibility|1,s&&(n===null||S||rn||jt||ia(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){S=n=e;try{if(o=S.stateNode,s)h=o.style,typeof h.setProperty=="function"?h.setProperty("display","none","important"):h.display="none";else{g=S.stateNode;var w=S.memoizedProps.style,R=w!=null&&w.hasOwnProperty("display")?w.display:null;g.style.display=R==null||typeof R=="boolean"?"":(""+R).trim()}}catch(O){xt(S,S.return,O)}}}else if(e.tag===6){if(n===null){S=e;try{S.stateNode.nodeValue=s?"":S.memoizedProps}catch(O){xt(S,S.return,O)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}i&4&&(i=t.updateQueue,i!==null&&(n=i.retryQueue,n!==null&&(i.retryQueue=null,Ur(t,n))));break;case 19:ye(e,t),ge(t),i&4&&(i=t.updateQueue,i!==null&&(t.updateQueue=null,Ur(t,i)));break;case 30:break;case 21:break;default:ye(e,t),ge(t)}}function ge(t){var e=t.flags;if(e&2){try{for(var n,i=t.return;i!==null;){if(Rd(i)){n=i;break}i=i.return}if(n==null)throw Error(r(160));switch(n.tag){case 27:var s=n.stateNode,o=zr(t);ys(t,o,s);break;case 5:var h=n.stateNode;n.flags&32&&(Sa(h,""),n.flags&=-33);var g=zr(t);ys(t,g,h);break;case 3:case 4:var S=n.stateNode.containerInfo,D=zr(t);wr(t,D,S);break;default:throw Error(r(161))}}catch(N){xt(t,t.return,N)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function jd(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;jd(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function Dn(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)_d(t,e.alternate,e),e=e.sibling}function ia(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:Mn(4,e,e.return),ia(e);break;case 1:Xe(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&Md(e,e.return,n),ia(e);break;case 27:Fi(e.stateNode);case 26:case 5:Xe(e,e.return),ia(e);break;case 22:e.memoizedState===null&&ia(e);break;case 30:ia(e);break;default:ia(e)}t=t.sibling}}function Rn(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var i=e.alternate,s=t,o=e,h=o.flags;switch(o.tag){case 0:case 11:case 15:Rn(s,o,n),qi(4,o);break;case 1:if(Rn(s,o,n),i=o,s=i.stateNode,typeof s.componentDidMount=="function")try{s.componentDidMount()}catch(D){xt(i,i.return,D)}if(i=o,s=i.updateQueue,s!==null){var g=i.stateNode;try{var S=s.shared.hiddenCallbacks;if(S!==null)for(s.shared.hiddenCallbacks=null,s=0;s<S.length;s++)ch(S[s],g)}catch(D){xt(i,i.return,D)}}n&&h&64&&Ed(o),Yi(o,o.return);break;case 27:Od(o);case 26:case 5:Rn(s,o,n),n&&i===null&&h&4&&Dd(o),Yi(o,o.return);break;case 12:Rn(s,o,n);break;case 13:Rn(s,o,n),n&&h&4&&zd(s,o);break;case 22:o.memoizedState===null&&Rn(s,o,n),Yi(o,o.return);break;case 30:break;default:Rn(s,o,n)}e=e.sibling}}function Br(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&Di(n))}function Lr(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Di(t))}function Ze(t,e,n,i){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Ud(t,e,n,i),e=e.sibling}function Ud(t,e,n,i){var s=e.flags;switch(e.tag){case 0:case 11:case 15:Ze(t,e,n,i),s&2048&&qi(9,e);break;case 1:Ze(t,e,n,i);break;case 3:Ze(t,e,n,i),s&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Di(t)));break;case 12:if(s&2048){Ze(t,e,n,i),t=e.stateNode;try{var o=e.memoizedProps,h=o.id,g=o.onPostCommit;typeof g=="function"&&g(h,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(S){xt(e,e.return,S)}}else Ze(t,e,n,i);break;case 13:Ze(t,e,n,i);break;case 23:break;case 22:o=e.stateNode,h=e.alternate,e.memoizedState!==null?o._visibility&2?Ze(t,e,n,i):Gi(t,e):o._visibility&2?Ze(t,e,n,i):(o._visibility|=2,Ha(t,e,n,i,(e.subtreeFlags&10256)!==0)),s&2048&&Br(h,e);break;case 24:Ze(t,e,n,i),s&2048&&Lr(e.alternate,e);break;default:Ze(t,e,n,i)}}function Ha(t,e,n,i,s){for(s=s&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var o=t,h=e,g=n,S=i,D=h.flags;switch(h.tag){case 0:case 11:case 15:Ha(o,h,g,S,s),qi(8,h);break;case 23:break;case 22:var N=h.stateNode;h.memoizedState!==null?N._visibility&2?Ha(o,h,g,S,s):Gi(o,h):(N._visibility|=2,Ha(o,h,g,S,s)),s&&D&2048&&Br(h.alternate,h);break;case 24:Ha(o,h,g,S,s),s&&D&2048&&Lr(h.alternate,h);break;default:Ha(o,h,g,S,s)}e=e.sibling}}function Gi(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,i=e,s=i.flags;switch(i.tag){case 22:Gi(n,i),s&2048&&Br(i.alternate,i);break;case 24:Gi(n,i),s&2048&&Lr(i.alternate,i);break;default:Gi(n,i)}e=e.sibling}}var Xi=8192;function qa(t){if(t.subtreeFlags&Xi)for(t=t.child;t!==null;)Bd(t),t=t.sibling}function Bd(t){switch(t.tag){case 26:qa(t),t.flags&Xi&&t.memoizedState!==null&&h1(we,t.memoizedState,t.memoizedProps);break;case 5:qa(t);break;case 3:case 4:var e=we;we=Cs(t.stateNode.containerInfo),qa(t),we=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Xi,Xi=16777216,qa(t),Xi=e):qa(t));break;default:qa(t)}}function Ld(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Zi(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var i=e[n];Kt=i,qd(i,t)}Ld(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Hd(t),t=t.sibling}function Hd(t){switch(t.tag){case 0:case 11:case 15:Zi(t),t.flags&2048&&Mn(9,t,t.return);break;case 3:Zi(t);break;case 12:Zi(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,gs(t)):Zi(t);break;default:Zi(t)}}function gs(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var i=e[n];Kt=i,qd(i,t)}Ld(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:Mn(8,e,e.return),gs(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,gs(e));break;default:gs(e)}t=t.sibling}}function qd(t,e){for(;Kt!==null;){var n=Kt;switch(n.tag){case 0:case 11:case 15:Mn(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var i=n.memoizedState.cachePool.pool;i!=null&&i.refCount++}break;case 24:Di(n.memoizedState.cache)}if(i=n.child,i!==null)i.return=n,Kt=i;else t:for(n=t;Kt!==null;){i=Kt;var s=i.sibling,o=i.return;if(Nd(i),i===n){Kt=null;break t}if(s!==null){s.return=o,Kt=s;break t}Kt=o}}}var Ov={getCacheForType:function(t){var e=te(Yt),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},Cv=typeof WeakMap=="function"?WeakMap:Map,gt=0,At=null,ot=null,ft=0,vt=0,ve=null,On=!1,Ya=!1,Hr=!1,cn=0,Vt=0,Cn=0,la=0,qr=0,Oe=0,Ga=0,Qi=null,oe=null,Yr=!1,Gr=0,vs=1/0,bs=null,_n=null,Ft=0,Nn=null,Xa=null,Za=0,Xr=0,Zr=null,Yd=null,Ki=0,Qr=null;function be(){if((gt&2)!==0&&ft!==0)return ft&-ft;if(V.T!==null){var t=Na;return t!==0?t:$r()}return nf()}function Gd(){Oe===0&&(Oe=(ft&536870912)===0||pt?$c():536870912);var t=Re.current;return t!==null&&(t.flags|=32),Oe}function Se(t,e,n){(t===At&&(vt===2||vt===9)||t.cancelPendingCommit!==null)&&(Qa(t,0),Vn(t,ft,Oe,!1)),ci(t,n),((gt&2)===0||t!==At)&&(t===At&&((gt&2)===0&&(la|=n),Vt===4&&Vn(t,ft,Oe,!1)),Qe(t))}function Xd(t,e,n){if((gt&6)!==0)throw Error(r(327));var i=!n&&(e&124)===0&&(e&t.expiredLanes)===0||oi(t,e),s=i?Vv(t,e):Pr(t,e,!0),o=i;do{if(s===0){Ya&&!i&&Vn(t,e,0,!1);break}else{if(n=t.current.alternate,o&&!_v(n)){s=Pr(t,e,!1),o=!1;continue}if(s===2){if(o=e,t.errorRecoveryDisabledLanes&o)var h=0;else h=t.pendingLanes&-536870913,h=h!==0?h:h&536870912?536870912:0;if(h!==0){e=h;t:{var g=t;s=Qi;var S=g.current.memoizedState.isDehydrated;if(S&&(Qa(g,h).flags|=256),h=Pr(g,h,!1),h!==2){if(Hr&&!S){g.errorRecoveryDisabledLanes|=o,la|=o,s=4;break t}o=oe,oe=s,o!==null&&(oe===null?oe=o:oe.push.apply(oe,o))}s=h}if(o=!1,s!==2)continue}}if(s===1){Qa(t,0),Vn(t,e,0,!0);break}t:{switch(i=t,o=s,o){case 0:case 1:throw Error(r(345));case 4:if((e&4194048)!==e)break;case 6:Vn(i,e,Oe,!On);break t;case 2:oe=null;break;case 3:case 5:break;default:throw Error(r(329))}if((e&62914560)===e&&(s=Gr+300-qe(),10<s)){if(Vn(i,e,Oe,!On),_l(i,0,!0)!==0)break t;i.timeoutHandle=vm(Zd.bind(null,i,n,oe,bs,Yr,e,Oe,la,Ga,On,o,2,-0,0),s);break t}Zd(i,n,oe,bs,Yr,e,Oe,la,Ga,On,o,0,-0,0)}}break}while(!0);Qe(t)}function Zd(t,e,n,i,s,o,h,g,S,D,N,w,R,O){if(t.timeoutHandle=-1,w=e.subtreeFlags,(w&8192||(w&16785408)===16785408)&&(Ii={stylesheets:null,count:0,unsuspend:f1},Bd(e),w=d1(),w!==null)){t.cancelPendingCommit=w(Wd.bind(null,t,e,o,n,i,s,h,g,S,N,1,R,O)),Vn(t,o,h,!D);return}Wd(t,e,o,n,i,s,h,g,S)}function _v(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var i=0;i<n.length;i++){var s=n[i],o=s.getSnapshot;s=s.value;try{if(!me(o(),s))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Vn(t,e,n,i){e&=~qr,e&=~la,t.suspendedLanes|=e,t.pingedLanes&=~e,i&&(t.warmLanes|=e),i=t.expirationTimes;for(var s=e;0<s;){var o=31-de(s),h=1<<o;i[o]=-1,s&=~h}n!==0&&tf(t,n,e)}function Ss(){return(gt&6)===0?(ki(0),!1):!0}function Kr(){if(ot!==null){if(vt===0)var t=ot.return;else t=ot,en=In=null,or(t),Ba=null,Bi=0,t=ot;for(;t!==null;)Ad(t.alternate,t),t=t.return;ot=null}}function Qa(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,Pv(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),Kr(),At=t,ot=n=$e(t.current,null),ft=e,vt=0,ve=null,On=!1,Ya=oi(t,e),Hr=!1,Ga=Oe=qr=la=Cn=Vt=0,oe=Qi=null,Yr=!1,(e&8)!==0&&(e|=e&32);var i=t.entangledLanes;if(i!==0)for(t=t.entanglements,i&=e;0<i;){var s=31-de(i),o=1<<s;e|=t[s],i&=~o}return cn=e,Gl(),n}function Qd(t,e){ut=null,V.H=us,e===Oi||e===Wl?(e=rh(),vt=3):e===lh?(e=rh(),vt=4):vt=e===od?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,ve=e,ot===null&&(Vt=1,hs(t,Ae(e,t.current)))}function Kd(){var t=V.H;return V.H=us,t===null?us:t}function kd(){var t=V.A;return V.A=Ov,t}function kr(){Vt=4,On||(ft&4194048)!==ft&&Re.current!==null||(Ya=!0),(Cn&134217727)===0&&(la&134217727)===0||At===null||Vn(At,ft,Oe,!1)}function Pr(t,e,n){var i=gt;gt|=2;var s=Kd(),o=kd();(At!==t||ft!==e)&&(bs=null,Qa(t,e)),e=!1;var h=Vt;t:do try{if(vt!==0&&ot!==null){var g=ot,S=ve;switch(vt){case 8:Kr(),h=6;break t;case 3:case 2:case 9:case 6:Re.current===null&&(e=!0);var D=vt;if(vt=0,ve=null,Ka(t,g,S,D),n&&Ya){h=0;break t}break;default:D=vt,vt=0,ve=null,Ka(t,g,S,D)}}Nv(),h=Vt;break}catch(N){Qd(t,N)}while(!0);return e&&t.shellSuspendCounter++,en=In=null,gt=i,V.H=s,V.A=o,ot===null&&(At=null,ft=0,Gl()),h}function Nv(){for(;ot!==null;)Pd(ot)}function Vv(t,e){var n=gt;gt|=2;var i=Kd(),s=kd();At!==t||ft!==e?(bs=null,vs=qe()+500,Qa(t,e)):Ya=oi(t,e);t:do try{if(vt!==0&&ot!==null){e=ot;var o=ve;e:switch(vt){case 1:vt=0,ve=null,Ka(t,e,o,1);break;case 2:case 9:if(sh(o)){vt=0,ve=null,Jd(e);break}e=function(){vt!==2&&vt!==9||At!==t||(vt=7),Qe(t)},o.then(e,e);break t;case 3:vt=7;break t;case 4:vt=5;break t;case 7:sh(o)?(vt=0,ve=null,Jd(e)):(vt=0,ve=null,Ka(t,e,o,7));break;case 5:var h=null;switch(ot.tag){case 26:h=ot.memoizedState;case 5:case 27:var g=ot;if(!h||_m(h)){vt=0,ve=null;var S=g.sibling;if(S!==null)ot=S;else{var D=g.return;D!==null?(ot=D,Ts(D)):ot=null}break e}}vt=0,ve=null,Ka(t,e,o,5);break;case 6:vt=0,ve=null,Ka(t,e,o,6);break;case 8:Kr(),Vt=6;break t;default:throw Error(r(462))}}zv();break}catch(N){Qd(t,N)}while(!0);return en=In=null,V.H=i,V.A=s,gt=n,ot!==null?0:(At=null,ft=0,Gl(),Vt)}function zv(){for(;ot!==null&&!eg();)Pd(ot)}function Pd(t){var e=Td(t.alternate,t,cn);t.memoizedProps=t.pendingProps,e===null?Ts(t):ot=e}function Jd(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=pd(n,e,e.pendingProps,e.type,void 0,ft);break;case 11:e=pd(n,e,e.pendingProps,e.type.render,e.ref,ft);break;case 5:or(e);default:Ad(n,e),e=ot=Ff(e,cn),e=Td(n,e,cn)}t.memoizedProps=t.pendingProps,e===null?Ts(t):ot=e}function Ka(t,e,n,i){en=In=null,or(e),Ba=null,Bi=0;var s=e.return;try{if(xv(t,s,e,n,ft)){Vt=1,hs(t,Ae(n,t.current)),ot=null;return}}catch(o){if(s!==null)throw ot=s,o;Vt=1,hs(t,Ae(n,t.current)),ot=null;return}e.flags&32768?(pt||i===1?t=!0:Ya||(ft&536870912)!==0?t=!1:(On=t=!0,(i===2||i===9||i===3||i===6)&&(i=Re.current,i!==null&&i.tag===13&&(i.flags|=16384))),Fd(e,t)):Ts(e)}function Ts(t){var e=t;do{if((e.flags&32768)!==0){Fd(e,On);return}t=e.return;var n=Ev(e.alternate,e,cn);if(n!==null){ot=n;return}if(e=e.sibling,e!==null){ot=e;return}ot=e=t}while(e!==null);Vt===0&&(Vt=5)}function Fd(t,e){do{var n=Mv(t.alternate,t);if(n!==null){n.flags&=32767,ot=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){ot=t;return}ot=t=n}while(t!==null);Vt=6,ot=null}function Wd(t,e,n,i,s,o,h,g,S){t.cancelPendingCommit=null;do xs();while(Ft!==0);if((gt&6)!==0)throw Error(r(327));if(e!==null){if(e===t.current)throw Error(r(177));if(o=e.lanes|e.childLanes,o|=Lu,fg(t,n,o,h,g,S),t===At&&(ot=At=null,ft=0),Xa=e,Nn=t,Za=n,Xr=o,Zr=s,Yd=i,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,Bv(Rl,function(){return nm(),null})):(t.callbackNode=null,t.callbackPriority=0),i=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||i){i=V.T,V.T=null,s=B.p,B.p=2,h=gt,gt|=4;try{Dv(t,e,n)}finally{gt=h,B.p=s,V.T=i}}Ft=1,$d(),Id(),tm()}}function $d(){if(Ft===1){Ft=0;var t=Nn,e=Xa,n=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||n){n=V.T,V.T=null;var i=B.p;B.p=2;var s=gt;gt|=4;try{wd(e,t);var o=so,h=qf(t.containerInfo),g=o.focusedElem,S=o.selectionRange;if(h!==g&&g&&g.ownerDocument&&Hf(g.ownerDocument.documentElement,g)){if(S!==null&&zu(g)){var D=S.start,N=S.end;if(N===void 0&&(N=D),"selectionStart"in g)g.selectionStart=D,g.selectionEnd=Math.min(N,g.value.length);else{var w=g.ownerDocument||document,R=w&&w.defaultView||window;if(R.getSelection){var O=R.getSelection(),at=g.textContent.length,I=Math.min(S.start,at),Tt=S.end===void 0?I:Math.min(S.end,at);!O.extend&&I>Tt&&(h=Tt,Tt=I,I=h);var E=Lf(g,I),A=Lf(g,Tt);if(E&&A&&(O.rangeCount!==1||O.anchorNode!==E.node||O.anchorOffset!==E.offset||O.focusNode!==A.node||O.focusOffset!==A.offset)){var M=w.createRange();M.setStart(E.node,E.offset),O.removeAllRanges(),I>Tt?(O.addRange(M),O.extend(A.node,A.offset)):(M.setEnd(A.node,A.offset),O.addRange(M))}}}}for(w=[],O=g;O=O.parentNode;)O.nodeType===1&&w.push({element:O,left:O.scrollLeft,top:O.scrollTop});for(typeof g.focus=="function"&&g.focus(),g=0;g<w.length;g++){var z=w[g];z.element.scrollLeft=z.left,z.element.scrollTop=z.top}}ws=!!lo,so=lo=null}finally{gt=s,B.p=i,V.T=n}}t.current=e,Ft=2}}function Id(){if(Ft===2){Ft=0;var t=Nn,e=Xa,n=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||n){n=V.T,V.T=null;var i=B.p;B.p=2;var s=gt;gt|=4;try{_d(t,e.alternate,e)}finally{gt=s,B.p=i,V.T=n}}Ft=3}}function tm(){if(Ft===4||Ft===3){Ft=0,ng();var t=Nn,e=Xa,n=Za,i=Yd;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Ft=5:(Ft=0,Xa=Nn=null,em(t,t.pendingLanes));var s=t.pendingLanes;if(s===0&&(_n=null),hu(n),e=e.stateNode,he&&typeof he.onCommitFiberRoot=="function")try{he.onCommitFiberRoot(ri,e,void 0,(e.current.flags&128)===128)}catch{}if(i!==null){e=V.T,s=B.p,B.p=2,V.T=null;try{for(var o=t.onRecoverableError,h=0;h<i.length;h++){var g=i[h];o(g.value,{componentStack:g.stack})}}finally{V.T=e,B.p=s}}(Za&3)!==0&&xs(),Qe(t),s=t.pendingLanes,(n&4194090)!==0&&(s&42)!==0?t===Qr?Ki++:(Ki=0,Qr=t):Ki=0,ki(0)}}function em(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Di(e)))}function xs(t){return $d(),Id(),tm(),nm()}function nm(){if(Ft!==5)return!1;var t=Nn,e=Xr;Xr=0;var n=hu(Za),i=V.T,s=B.p;try{B.p=32>n?32:n,V.T=null,n=Zr,Zr=null;var o=Nn,h=Za;if(Ft=0,Xa=Nn=null,Za=0,(gt&6)!==0)throw Error(r(331));var g=gt;if(gt|=4,Hd(o.current),Ud(o,o.current,h,n),gt=g,ki(0,!1),he&&typeof he.onPostCommitFiberRoot=="function")try{he.onPostCommitFiberRoot(ri,o)}catch{}return!0}finally{B.p=s,V.T=i,em(t,e)}}function am(t,e,n){e=Ae(n,e),e=Ar(t.stateNode,e,2),t=Tn(t,e,2),t!==null&&(ci(t,2),Qe(t))}function xt(t,e,n){if(t.tag===3)am(t,t,n);else for(;e!==null;){if(e.tag===3){am(e,t,n);break}else if(e.tag===1){var i=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(_n===null||!_n.has(i))){t=Ae(n,t),n=ud(2),i=Tn(e,n,2),i!==null&&(rd(n,i,e,t),ci(i,2),Qe(i));break}}e=e.return}}function Jr(t,e,n){var i=t.pingCache;if(i===null){i=t.pingCache=new Cv;var s=new Set;i.set(e,s)}else s=i.get(e),s===void 0&&(s=new Set,i.set(e,s));s.has(n)||(Hr=!0,s.add(n),t=wv.bind(null,t,e,n),e.then(t,t))}function wv(t,e,n){var i=t.pingCache;i!==null&&i.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,At===t&&(ft&n)===n&&(Vt===4||Vt===3&&(ft&62914560)===ft&&300>qe()-Gr?(gt&2)===0&&Qa(t,0):qr|=n,Ga===ft&&(Ga=0)),Qe(t)}function im(t,e){e===0&&(e=Ic()),t=Ra(t,e),t!==null&&(ci(t,e),Qe(t))}function jv(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),im(t,n)}function Uv(t,e){var n=0;switch(t.tag){case 13:var i=t.stateNode,s=t.memoizedState;s!==null&&(n=s.retryLane);break;case 19:i=t.stateNode;break;case 22:i=t.stateNode._retryCache;break;default:throw Error(r(314))}i!==null&&i.delete(e),im(t,n)}function Bv(t,e){return ru(t,e)}var As=null,ka=null,Fr=!1,Es=!1,Wr=!1,sa=0;function Qe(t){t!==ka&&t.next===null&&(ka===null?As=ka=t:ka=ka.next=t),Es=!0,Fr||(Fr=!0,Hv())}function ki(t,e){if(!Wr&&Es){Wr=!0;do for(var n=!1,i=As;i!==null;){if(t!==0){var s=i.pendingLanes;if(s===0)var o=0;else{var h=i.suspendedLanes,g=i.pingedLanes;o=(1<<31-de(42|t)+1)-1,o&=s&~(h&~g),o=o&201326741?o&201326741|1:o?o|2:0}o!==0&&(n=!0,rm(i,o))}else o=ft,o=_l(i,i===At?o:0,i.cancelPendingCommit!==null||i.timeoutHandle!==-1),(o&3)===0||oi(i,o)||(n=!0,rm(i,o));i=i.next}while(n);Wr=!1}}function Lv(){lm()}function lm(){Es=Fr=!1;var t=0;sa!==0&&(kv()&&(t=sa),sa=0);for(var e=qe(),n=null,i=As;i!==null;){var s=i.next,o=sm(i,e);o===0?(i.next=null,n===null?As=s:n.next=s,s===null&&(ka=n)):(n=i,(t!==0||(o&3)!==0)&&(Es=!0)),i=s}ki(t)}function sm(t,e){for(var n=t.suspendedLanes,i=t.pingedLanes,s=t.expirationTimes,o=t.pendingLanes&-62914561;0<o;){var h=31-de(o),g=1<<h,S=s[h];S===-1?((g&n)===0||(g&i)!==0)&&(s[h]=cg(g,e)):S<=e&&(t.expiredLanes|=g),o&=~g}if(e=At,n=ft,n=_l(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),i=t.callbackNode,n===0||t===e&&(vt===2||vt===9)||t.cancelPendingCommit!==null)return i!==null&&i!==null&&ou(i),t.callbackNode=null,t.callbackPriority=0;if((n&3)===0||oi(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(i!==null&&ou(i),hu(n)){case 2:case 8:n=Fc;break;case 32:n=Rl;break;case 268435456:n=Wc;break;default:n=Rl}return i=um.bind(null,t),n=ru(n,i),t.callbackPriority=e,t.callbackNode=n,e}return i!==null&&i!==null&&ou(i),t.callbackPriority=2,t.callbackNode=null,2}function um(t,e){if(Ft!==0&&Ft!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(xs()&&t.callbackNode!==n)return null;var i=ft;return i=_l(t,t===At?i:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),i===0?null:(Xd(t,i,e),sm(t,qe()),t.callbackNode!=null&&t.callbackNode===n?um.bind(null,t):null)}function rm(t,e){if(xs())return null;Xd(t,e,!0)}function Hv(){Jv(function(){(gt&6)!==0?ru(Jc,Lv):lm()})}function $r(){return sa===0&&(sa=$c()),sa}function om(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:jl(""+t)}function cm(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function qv(t,e,n,i,s){if(e==="submit"&&n&&n.stateNode===s){var o=om((s[le]||null).action),h=i.submitter;h&&(e=(e=h[le]||null)?om(e.formAction):h.getAttribute("formAction"),e!==null&&(o=e,h=null));var g=new Hl("action","action",null,i,s);t.push({event:g,listeners:[{instance:null,listener:function(){if(i.defaultPrevented){if(sa!==0){var S=h?cm(s,h):new FormData(s);vr(n,{pending:!0,data:S,method:s.method,action:o},null,S)}}else typeof o=="function"&&(g.preventDefault(),S=h?cm(s,h):new FormData(s),vr(n,{pending:!0,data:S,method:s.method,action:o},o,S))},currentTarget:s}]})}}for(var Ir=0;Ir<Bu.length;Ir++){var to=Bu[Ir],Yv=to.toLowerCase(),Gv=to[0].toUpperCase()+to.slice(1);ze(Yv,"on"+Gv)}ze(Xf,"onAnimationEnd"),ze(Zf,"onAnimationIteration"),ze(Qf,"onAnimationStart"),ze("dblclick","onDoubleClick"),ze("focusin","onFocus"),ze("focusout","onBlur"),ze(lv,"onTransitionRun"),ze(sv,"onTransitionStart"),ze(uv,"onTransitionCancel"),ze(Kf,"onTransitionEnd"),ga("onMouseEnter",["mouseout","mouseover"]),ga("onMouseLeave",["mouseout","mouseover"]),ga("onPointerEnter",["pointerout","pointerover"]),ga("onPointerLeave",["pointerout","pointerover"]),Zn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Zn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Zn("onBeforeInput",["compositionend","keypress","textInput","paste"]),Zn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Zn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Zn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Pi="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Xv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Pi));function fm(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var i=t[n],s=i.event;i=i.listeners;t:{var o=void 0;if(e)for(var h=i.length-1;0<=h;h--){var g=i[h],S=g.instance,D=g.currentTarget;if(g=g.listener,S!==o&&s.isPropagationStopped())break t;o=g,s.currentTarget=D;try{o(s)}catch(N){fs(N)}s.currentTarget=null,o=S}else for(h=0;h<i.length;h++){if(g=i[h],S=g.instance,D=g.currentTarget,g=g.listener,S!==o&&s.isPropagationStopped())break t;o=g,s.currentTarget=D;try{o(s)}catch(N){fs(N)}s.currentTarget=null,o=S}}}}function ct(t,e){var n=e[du];n===void 0&&(n=e[du]=new Set);var i=t+"__bubble";n.has(i)||(hm(e,t,2,!1),n.add(i))}function eo(t,e,n){var i=0;e&&(i|=4),hm(n,t,i,e)}var Ms="_reactListening"+Math.random().toString(36).slice(2);function no(t){if(!t[Ms]){t[Ms]=!0,lf.forEach(function(n){n!=="selectionchange"&&(Xv.has(n)||eo(n,!1,t),eo(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Ms]||(e[Ms]=!0,eo("selectionchange",!1,e))}}function hm(t,e,n,i){switch(Um(e)){case 2:var s=y1;break;case 8:s=g1;break;default:s=go}n=s.bind(null,e,n,t),s=void 0,!Eu||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(s=!0),i?s!==void 0?t.addEventListener(e,n,{capture:!0,passive:s}):t.addEventListener(e,n,!0):s!==void 0?t.addEventListener(e,n,{passive:s}):t.addEventListener(e,n,!1)}function ao(t,e,n,i,s){var o=i;if((e&1)===0&&(e&2)===0&&i!==null)t:for(;;){if(i===null)return;var h=i.tag;if(h===3||h===4){var g=i.stateNode.containerInfo;if(g===s)break;if(h===4)for(h=i.return;h!==null;){var S=h.tag;if((S===3||S===4)&&h.stateNode.containerInfo===s)return;h=h.return}for(;g!==null;){if(h=ma(g),h===null)return;if(S=h.tag,S===5||S===6||S===26||S===27){i=o=h;continue t}g=g.parentNode}}i=i.return}bf(function(){var D=o,N=xu(n),w=[];t:{var R=kf.get(t);if(R!==void 0){var O=Hl,at=t;switch(t){case"keypress":if(Bl(n)===0)break t;case"keydown":case"keyup":O=Bg;break;case"focusin":at="focus",O=Ou;break;case"focusout":at="blur",O=Ou;break;case"beforeblur":case"afterblur":O=Ou;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":O=xf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":O=Mg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":O=qg;break;case Xf:case Zf:case Qf:O=Og;break;case Kf:O=Gg;break;case"scroll":case"scrollend":O=Ag;break;case"wheel":O=Zg;break;case"copy":case"cut":case"paste":O=_g;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":O=Ef;break;case"toggle":case"beforetoggle":O=Kg}var I=(e&4)!==0,Tt=!I&&(t==="scroll"||t==="scrollend"),E=I?R!==null?R+"Capture":null:R;I=[];for(var A=D,M;A!==null;){var z=A;if(M=z.stateNode,z=z.tag,z!==5&&z!==26&&z!==27||M===null||E===null||(z=di(A,E),z!=null&&I.push(Ji(A,z,M))),Tt)break;A=A.return}0<I.length&&(R=new O(R,at,null,n,N),w.push({event:R,listeners:I}))}}if((e&7)===0){t:{if(R=t==="mouseover"||t==="pointerover",O=t==="mouseout"||t==="pointerout",R&&n!==Tu&&(at=n.relatedTarget||n.fromElement)&&(ma(at)||at[da]))break t;if((O||R)&&(R=N.window===N?N:(R=N.ownerDocument)?R.defaultView||R.parentWindow:window,O?(at=n.relatedTarget||n.toElement,O=D,at=at?ma(at):null,at!==null&&(Tt=d(at),I=at.tag,at!==Tt||I!==5&&I!==27&&I!==6)&&(at=null)):(O=null,at=D),O!==at)){if(I=xf,z="onMouseLeave",E="onMouseEnter",A="mouse",(t==="pointerout"||t==="pointerover")&&(I=Ef,z="onPointerLeave",E="onPointerEnter",A="pointer"),Tt=O==null?R:hi(O),M=at==null?R:hi(at),R=new I(z,A+"leave",O,n,N),R.target=Tt,R.relatedTarget=M,z=null,ma(N)===D&&(I=new I(E,A+"enter",at,n,N),I.target=M,I.relatedTarget=Tt,z=I),Tt=z,O&&at)e:{for(I=O,E=at,A=0,M=I;M;M=Pa(M))A++;for(M=0,z=E;z;z=Pa(z))M++;for(;0<A-M;)I=Pa(I),A--;for(;0<M-A;)E=Pa(E),M--;for(;A--;){if(I===E||E!==null&&I===E.alternate)break e;I=Pa(I),E=Pa(E)}I=null}else I=null;O!==null&&dm(w,R,O,I,!1),at!==null&&Tt!==null&&dm(w,Tt,at,I,!0)}}t:{if(R=D?hi(D):window,O=R.nodeName&&R.nodeName.toLowerCase(),O==="select"||O==="input"&&R.type==="file")var k=Vf;else if(_f(R))if(zf)k=nv;else{k=tv;var rt=Ig}else O=R.nodeName,!O||O.toLowerCase()!=="input"||R.type!=="checkbox"&&R.type!=="radio"?D&&Su(D.elementType)&&(k=Vf):k=ev;if(k&&(k=k(t,D))){Nf(w,k,n,N);break t}rt&&rt(t,R,D),t==="focusout"&&D&&R.type==="number"&&D.memoizedProps.value!=null&&bu(R,"number",R.value)}switch(rt=D?hi(D):window,t){case"focusin":(_f(rt)||rt.contentEditable==="true")&&(Ea=rt,wu=D,Ti=null);break;case"focusout":Ti=wu=Ea=null;break;case"mousedown":ju=!0;break;case"contextmenu":case"mouseup":case"dragend":ju=!1,Yf(w,n,N);break;case"selectionchange":if(iv)break;case"keydown":case"keyup":Yf(w,n,N)}var F;if(_u)t:{switch(t){case"compositionstart":var et="onCompositionStart";break t;case"compositionend":et="onCompositionEnd";break t;case"compositionupdate":et="onCompositionUpdate";break t}et=void 0}else Aa?Of(t,n)&&(et="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(et="onCompositionStart");et&&(Mf&&n.locale!=="ko"&&(Aa||et!=="onCompositionStart"?et==="onCompositionEnd"&&Aa&&(F=Sf()):(gn=N,Mu="value"in gn?gn.value:gn.textContent,Aa=!0)),rt=Ds(D,et),0<rt.length&&(et=new Af(et,t,null,n,N),w.push({event:et,listeners:rt}),F?et.data=F:(F=Cf(n),F!==null&&(et.data=F)))),(F=Pg?Jg(t,n):Fg(t,n))&&(et=Ds(D,"onBeforeInput"),0<et.length&&(rt=new Af("onBeforeInput","beforeinput",null,n,N),w.push({event:rt,listeners:et}),rt.data=F)),qv(w,t,D,n,N)}fm(w,e)})}function Ji(t,e,n){return{instance:t,listener:e,currentTarget:n}}function Ds(t,e){for(var n=e+"Capture",i=[];t!==null;){var s=t,o=s.stateNode;if(s=s.tag,s!==5&&s!==26&&s!==27||o===null||(s=di(t,n),s!=null&&i.unshift(Ji(t,s,o)),s=di(t,e),s!=null&&i.push(Ji(t,s,o))),t.tag===3)return i;t=t.return}return[]}function Pa(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function dm(t,e,n,i,s){for(var o=e._reactName,h=[];n!==null&&n!==i;){var g=n,S=g.alternate,D=g.stateNode;if(g=g.tag,S!==null&&S===i)break;g!==5&&g!==26&&g!==27||D===null||(S=D,s?(D=di(n,o),D!=null&&h.unshift(Ji(n,D,S))):s||(D=di(n,o),D!=null&&h.push(Ji(n,D,S)))),n=n.return}h.length!==0&&t.push({event:e,listeners:h})}var Zv=/\r\n?/g,Qv=/\u0000|\uFFFD/g;function mm(t){return(typeof t=="string"?t:""+t).replace(Zv,`
`).replace(Qv,"")}function pm(t,e){return e=mm(e),mm(t)===e}function Rs(){}function St(t,e,n,i,s,o){switch(n){case"children":typeof i=="string"?e==="body"||e==="textarea"&&i===""||Sa(t,i):(typeof i=="number"||typeof i=="bigint")&&e!=="body"&&Sa(t,""+i);break;case"className":Vl(t,"class",i);break;case"tabIndex":Vl(t,"tabindex",i);break;case"dir":case"role":case"viewBox":case"width":case"height":Vl(t,n,i);break;case"style":gf(t,i,o);break;case"data":if(e!=="object"){Vl(t,"data",i);break}case"src":case"href":if(i===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(i==null||typeof i=="function"||typeof i=="symbol"||typeof i=="boolean"){t.removeAttribute(n);break}i=jl(""+i),t.setAttribute(n,i);break;case"action":case"formAction":if(typeof i=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof o=="function"&&(n==="formAction"?(e!=="input"&&St(t,e,"name",s.name,s,null),St(t,e,"formEncType",s.formEncType,s,null),St(t,e,"formMethod",s.formMethod,s,null),St(t,e,"formTarget",s.formTarget,s,null)):(St(t,e,"encType",s.encType,s,null),St(t,e,"method",s.method,s,null),St(t,e,"target",s.target,s,null)));if(i==null||typeof i=="symbol"||typeof i=="boolean"){t.removeAttribute(n);break}i=jl(""+i),t.setAttribute(n,i);break;case"onClick":i!=null&&(t.onclick=Rs);break;case"onScroll":i!=null&&ct("scroll",t);break;case"onScrollEnd":i!=null&&ct("scrollend",t);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(r(61));if(n=i.__html,n!=null){if(s.children!=null)throw Error(r(60));t.innerHTML=n}}break;case"multiple":t.multiple=i&&typeof i!="function"&&typeof i!="symbol";break;case"muted":t.muted=i&&typeof i!="function"&&typeof i!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(i==null||typeof i=="function"||typeof i=="boolean"||typeof i=="symbol"){t.removeAttribute("xlink:href");break}n=jl(""+i),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":i!=null&&typeof i!="function"&&typeof i!="symbol"?t.setAttribute(n,""+i):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":i&&typeof i!="function"&&typeof i!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":i===!0?t.setAttribute(n,""):i!==!1&&i!=null&&typeof i!="function"&&typeof i!="symbol"?t.setAttribute(n,i):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":i!=null&&typeof i!="function"&&typeof i!="symbol"&&!isNaN(i)&&1<=i?t.setAttribute(n,i):t.removeAttribute(n);break;case"rowSpan":case"start":i==null||typeof i=="function"||typeof i=="symbol"||isNaN(i)?t.removeAttribute(n):t.setAttribute(n,i);break;case"popover":ct("beforetoggle",t),ct("toggle",t),Nl(t,"popover",i);break;case"xlinkActuate":Fe(t,"http://www.w3.org/1999/xlink","xlink:actuate",i);break;case"xlinkArcrole":Fe(t,"http://www.w3.org/1999/xlink","xlink:arcrole",i);break;case"xlinkRole":Fe(t,"http://www.w3.org/1999/xlink","xlink:role",i);break;case"xlinkShow":Fe(t,"http://www.w3.org/1999/xlink","xlink:show",i);break;case"xlinkTitle":Fe(t,"http://www.w3.org/1999/xlink","xlink:title",i);break;case"xlinkType":Fe(t,"http://www.w3.org/1999/xlink","xlink:type",i);break;case"xmlBase":Fe(t,"http://www.w3.org/XML/1998/namespace","xml:base",i);break;case"xmlLang":Fe(t,"http://www.w3.org/XML/1998/namespace","xml:lang",i);break;case"xmlSpace":Fe(t,"http://www.w3.org/XML/1998/namespace","xml:space",i);break;case"is":Nl(t,"is",i);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Tg.get(n)||n,Nl(t,n,i))}}function io(t,e,n,i,s,o){switch(n){case"style":gf(t,i,o);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(r(61));if(n=i.__html,n!=null){if(s.children!=null)throw Error(r(60));t.innerHTML=n}}break;case"children":typeof i=="string"?Sa(t,i):(typeof i=="number"||typeof i=="bigint")&&Sa(t,""+i);break;case"onScroll":i!=null&&ct("scroll",t);break;case"onScrollEnd":i!=null&&ct("scrollend",t);break;case"onClick":i!=null&&(t.onclick=Rs);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!sf.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(s=n.endsWith("Capture"),e=n.slice(2,s?n.length-7:void 0),o=t[le]||null,o=o!=null?o[n]:null,typeof o=="function"&&t.removeEventListener(e,o,s),typeof i=="function")){typeof o!="function"&&o!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,i,s);break t}n in t?t[n]=i:i===!0?t.setAttribute(n,""):Nl(t,n,i)}}}function Wt(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ct("error",t),ct("load",t);var i=!1,s=!1,o;for(o in n)if(n.hasOwnProperty(o)){var h=n[o];if(h!=null)switch(o){case"src":i=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:St(t,e,o,h,n,null)}}s&&St(t,e,"srcSet",n.srcSet,n,null),i&&St(t,e,"src",n.src,n,null);return;case"input":ct("invalid",t);var g=o=h=s=null,S=null,D=null;for(i in n)if(n.hasOwnProperty(i)){var N=n[i];if(N!=null)switch(i){case"name":s=N;break;case"type":h=N;break;case"checked":S=N;break;case"defaultChecked":D=N;break;case"value":o=N;break;case"defaultValue":g=N;break;case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(r(137,e));break;default:St(t,e,i,N,n,null)}}df(t,o,g,S,D,h,s,!1),zl(t);return;case"select":ct("invalid",t),i=h=o=null;for(s in n)if(n.hasOwnProperty(s)&&(g=n[s],g!=null))switch(s){case"value":o=g;break;case"defaultValue":h=g;break;case"multiple":i=g;default:St(t,e,s,g,n,null)}e=o,n=h,t.multiple=!!i,e!=null?ba(t,!!i,e,!1):n!=null&&ba(t,!!i,n,!0);return;case"textarea":ct("invalid",t),o=s=i=null;for(h in n)if(n.hasOwnProperty(h)&&(g=n[h],g!=null))switch(h){case"value":i=g;break;case"defaultValue":s=g;break;case"children":o=g;break;case"dangerouslySetInnerHTML":if(g!=null)throw Error(r(91));break;default:St(t,e,h,g,n,null)}pf(t,i,s,o),zl(t);return;case"option":for(S in n)if(n.hasOwnProperty(S)&&(i=n[S],i!=null))switch(S){case"selected":t.selected=i&&typeof i!="function"&&typeof i!="symbol";break;default:St(t,e,S,i,n,null)}return;case"dialog":ct("beforetoggle",t),ct("toggle",t),ct("cancel",t),ct("close",t);break;case"iframe":case"object":ct("load",t);break;case"video":case"audio":for(i=0;i<Pi.length;i++)ct(Pi[i],t);break;case"image":ct("error",t),ct("load",t);break;case"details":ct("toggle",t);break;case"embed":case"source":case"link":ct("error",t),ct("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(D in n)if(n.hasOwnProperty(D)&&(i=n[D],i!=null))switch(D){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:St(t,e,D,i,n,null)}return;default:if(Su(e)){for(N in n)n.hasOwnProperty(N)&&(i=n[N],i!==void 0&&io(t,e,N,i,n,void 0));return}}for(g in n)n.hasOwnProperty(g)&&(i=n[g],i!=null&&St(t,e,g,i,n,null))}function Kv(t,e,n,i){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var s=null,o=null,h=null,g=null,S=null,D=null,N=null;for(O in n){var w=n[O];if(n.hasOwnProperty(O)&&w!=null)switch(O){case"checked":break;case"value":break;case"defaultValue":S=w;default:i.hasOwnProperty(O)||St(t,e,O,null,i,w)}}for(var R in i){var O=i[R];if(w=n[R],i.hasOwnProperty(R)&&(O!=null||w!=null))switch(R){case"type":o=O;break;case"name":s=O;break;case"checked":D=O;break;case"defaultChecked":N=O;break;case"value":h=O;break;case"defaultValue":g=O;break;case"children":case"dangerouslySetInnerHTML":if(O!=null)throw Error(r(137,e));break;default:O!==w&&St(t,e,R,O,i,w)}}vu(t,h,g,S,D,N,o,s);return;case"select":O=h=g=R=null;for(o in n)if(S=n[o],n.hasOwnProperty(o)&&S!=null)switch(o){case"value":break;case"multiple":O=S;default:i.hasOwnProperty(o)||St(t,e,o,null,i,S)}for(s in i)if(o=i[s],S=n[s],i.hasOwnProperty(s)&&(o!=null||S!=null))switch(s){case"value":R=o;break;case"defaultValue":g=o;break;case"multiple":h=o;default:o!==S&&St(t,e,s,o,i,S)}e=g,n=h,i=O,R!=null?ba(t,!!n,R,!1):!!i!=!!n&&(e!=null?ba(t,!!n,e,!0):ba(t,!!n,n?[]:"",!1));return;case"textarea":O=R=null;for(g in n)if(s=n[g],n.hasOwnProperty(g)&&s!=null&&!i.hasOwnProperty(g))switch(g){case"value":break;case"children":break;default:St(t,e,g,null,i,s)}for(h in i)if(s=i[h],o=n[h],i.hasOwnProperty(h)&&(s!=null||o!=null))switch(h){case"value":R=s;break;case"defaultValue":O=s;break;case"children":break;case"dangerouslySetInnerHTML":if(s!=null)throw Error(r(91));break;default:s!==o&&St(t,e,h,s,i,o)}mf(t,R,O);return;case"option":for(var at in n)if(R=n[at],n.hasOwnProperty(at)&&R!=null&&!i.hasOwnProperty(at))switch(at){case"selected":t.selected=!1;break;default:St(t,e,at,null,i,R)}for(S in i)if(R=i[S],O=n[S],i.hasOwnProperty(S)&&R!==O&&(R!=null||O!=null))switch(S){case"selected":t.selected=R&&typeof R!="function"&&typeof R!="symbol";break;default:St(t,e,S,R,i,O)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var I in n)R=n[I],n.hasOwnProperty(I)&&R!=null&&!i.hasOwnProperty(I)&&St(t,e,I,null,i,R);for(D in i)if(R=i[D],O=n[D],i.hasOwnProperty(D)&&R!==O&&(R!=null||O!=null))switch(D){case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(r(137,e));break;default:St(t,e,D,R,i,O)}return;default:if(Su(e)){for(var Tt in n)R=n[Tt],n.hasOwnProperty(Tt)&&R!==void 0&&!i.hasOwnProperty(Tt)&&io(t,e,Tt,void 0,i,R);for(N in i)R=i[N],O=n[N],!i.hasOwnProperty(N)||R===O||R===void 0&&O===void 0||io(t,e,N,R,i,O);return}}for(var E in n)R=n[E],n.hasOwnProperty(E)&&R!=null&&!i.hasOwnProperty(E)&&St(t,e,E,null,i,R);for(w in i)R=i[w],O=n[w],!i.hasOwnProperty(w)||R===O||R==null&&O==null||St(t,e,w,R,i,O)}var lo=null,so=null;function Os(t){return t.nodeType===9?t:t.ownerDocument}function ym(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function gm(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function uo(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var ro=null;function kv(){var t=window.event;return t&&t.type==="popstate"?t===ro?!1:(ro=t,!0):(ro=null,!1)}var vm=typeof setTimeout=="function"?setTimeout:void 0,Pv=typeof clearTimeout=="function"?clearTimeout:void 0,bm=typeof Promise=="function"?Promise:void 0,Jv=typeof queueMicrotask=="function"?queueMicrotask:typeof bm<"u"?function(t){return bm.resolve(null).then(t).catch(Fv)}:vm;function Fv(t){setTimeout(function(){throw t})}function zn(t){return t==="head"}function Sm(t,e){var n=e,i=0,s=0;do{var o=n.nextSibling;if(t.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(0<i&&8>i){n=i;var h=t.ownerDocument;if(n&1&&Fi(h.documentElement),n&2&&Fi(h.body),n&4)for(n=h.head,Fi(n),h=n.firstChild;h;){var g=h.nextSibling,S=h.nodeName;h[fi]||S==="SCRIPT"||S==="STYLE"||S==="LINK"&&h.rel.toLowerCase()==="stylesheet"||n.removeChild(h),h=g}}if(s===0){t.removeChild(o),il(e);return}s--}else n==="$"||n==="$?"||n==="$!"?s++:i=n.charCodeAt(0)-48;else i=0;n=o}while(n);il(e)}function oo(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":oo(n),mu(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function Wv(t,e,n,i){for(;t.nodeType===1;){var s=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!i&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(i){if(!t[fi])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(o=t.getAttribute("rel"),o==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(o!==s.rel||t.getAttribute("href")!==(s.href==null||s.href===""?null:s.href)||t.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin)||t.getAttribute("title")!==(s.title==null?null:s.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(o=t.getAttribute("src"),(o!==(s.src==null?null:s.src)||t.getAttribute("type")!==(s.type==null?null:s.type)||t.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin))&&o&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var o=s.name==null?null:""+s.name;if(s.type==="hidden"&&t.getAttribute("name")===o)return t}else return t;if(t=je(t.nextSibling),t===null)break}return null}function $v(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=je(t.nextSibling),t===null))return null;return t}function co(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function Iv(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var i=function(){e(),n.removeEventListener("DOMContentLoaded",i)};n.addEventListener("DOMContentLoaded",i),t._reactRetry=i}}function je(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var fo=null;function Tm(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function xm(t,e,n){switch(e=Os(n),t){case"html":if(t=e.documentElement,!t)throw Error(r(452));return t;case"head":if(t=e.head,!t)throw Error(r(453));return t;case"body":if(t=e.body,!t)throw Error(r(454));return t;default:throw Error(r(451))}}function Fi(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);mu(t)}var Ce=new Map,Am=new Set;function Cs(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var fn=B.d;B.d={f:t1,r:e1,D:n1,C:a1,L:i1,m:l1,X:u1,S:s1,M:r1};function t1(){var t=fn.f(),e=Ss();return t||e}function e1(t){var e=pa(t);e!==null&&e.tag===5&&e.type==="form"?Xh(e):fn.r(t)}var Ja=typeof document>"u"?null:document;function Em(t,e,n){var i=Ja;if(i&&typeof e=="string"&&e){var s=xe(e);s='link[rel="'+t+'"][href="'+s+'"]',typeof n=="string"&&(s+='[crossorigin="'+n+'"]'),Am.has(s)||(Am.add(s),t={rel:t,crossOrigin:n,href:e},i.querySelector(s)===null&&(e=i.createElement("link"),Wt(e,"link",t),Zt(e),i.head.appendChild(e)))}}function n1(t){fn.D(t),Em("dns-prefetch",t,null)}function a1(t,e){fn.C(t,e),Em("preconnect",t,e)}function i1(t,e,n){fn.L(t,e,n);var i=Ja;if(i&&t&&e){var s='link[rel="preload"][as="'+xe(e)+'"]';e==="image"&&n&&n.imageSrcSet?(s+='[imagesrcset="'+xe(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(s+='[imagesizes="'+xe(n.imageSizes)+'"]')):s+='[href="'+xe(t)+'"]';var o=s;switch(e){case"style":o=Fa(t);break;case"script":o=Wa(t)}Ce.has(o)||(t=v({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),Ce.set(o,t),i.querySelector(s)!==null||e==="style"&&i.querySelector(Wi(o))||e==="script"&&i.querySelector($i(o))||(e=i.createElement("link"),Wt(e,"link",t),Zt(e),i.head.appendChild(e)))}}function l1(t,e){fn.m(t,e);var n=Ja;if(n&&t){var i=e&&typeof e.as=="string"?e.as:"script",s='link[rel="modulepreload"][as="'+xe(i)+'"][href="'+xe(t)+'"]',o=s;switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":o=Wa(t)}if(!Ce.has(o)&&(t=v({rel:"modulepreload",href:t},e),Ce.set(o,t),n.querySelector(s)===null)){switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector($i(o)))return}i=n.createElement("link"),Wt(i,"link",t),Zt(i),n.head.appendChild(i)}}}function s1(t,e,n){fn.S(t,e,n);var i=Ja;if(i&&t){var s=ya(i).hoistableStyles,o=Fa(t);e=e||"default";var h=s.get(o);if(!h){var g={loading:0,preload:null};if(h=i.querySelector(Wi(o)))g.loading=5;else{t=v({rel:"stylesheet",href:t,"data-precedence":e},n),(n=Ce.get(o))&&ho(t,n);var S=h=i.createElement("link");Zt(S),Wt(S,"link",t),S._p=new Promise(function(D,N){S.onload=D,S.onerror=N}),S.addEventListener("load",function(){g.loading|=1}),S.addEventListener("error",function(){g.loading|=2}),g.loading|=4,_s(h,e,i)}h={type:"stylesheet",instance:h,count:1,state:g},s.set(o,h)}}}function u1(t,e){fn.X(t,e);var n=Ja;if(n&&t){var i=ya(n).hoistableScripts,s=Wa(t),o=i.get(s);o||(o=n.querySelector($i(s)),o||(t=v({src:t,async:!0},e),(e=Ce.get(s))&&mo(t,e),o=n.createElement("script"),Zt(o),Wt(o,"link",t),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},i.set(s,o))}}function r1(t,e){fn.M(t,e);var n=Ja;if(n&&t){var i=ya(n).hoistableScripts,s=Wa(t),o=i.get(s);o||(o=n.querySelector($i(s)),o||(t=v({src:t,async:!0,type:"module"},e),(e=Ce.get(s))&&mo(t,e),o=n.createElement("script"),Zt(o),Wt(o,"link",t),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},i.set(s,o))}}function Mm(t,e,n,i){var s=(s=lt.current)?Cs(s):null;if(!s)throw Error(r(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=Fa(n.href),n=ya(s).hoistableStyles,i=n.get(e),i||(i={type:"style",instance:null,count:0,state:null},n.set(e,i)),i):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=Fa(n.href);var o=ya(s).hoistableStyles,h=o.get(t);if(h||(s=s.ownerDocument||s,h={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},o.set(t,h),(o=s.querySelector(Wi(t)))&&!o._p&&(h.instance=o,h.state.loading=5),Ce.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Ce.set(t,n),o||o1(s,t,n,h.state))),e&&i===null)throw Error(r(528,""));return h}if(e&&i!==null)throw Error(r(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Wa(n),n=ya(s).hoistableScripts,i=n.get(e),i||(i={type:"script",instance:null,count:0,state:null},n.set(e,i)),i):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,t))}}function Fa(t){return'href="'+xe(t)+'"'}function Wi(t){return'link[rel="stylesheet"]['+t+"]"}function Dm(t){return v({},t,{"data-precedence":t.precedence,precedence:null})}function o1(t,e,n,i){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?i.loading=1:(e=t.createElement("link"),i.preload=e,e.addEventListener("load",function(){return i.loading|=1}),e.addEventListener("error",function(){return i.loading|=2}),Wt(e,"link",n),Zt(e),t.head.appendChild(e))}function Wa(t){return'[src="'+xe(t)+'"]'}function $i(t){return"script[async]"+t}function Rm(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var i=t.querySelector('style[data-href~="'+xe(n.href)+'"]');if(i)return e.instance=i,Zt(i),i;var s=v({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return i=(t.ownerDocument||t).createElement("style"),Zt(i),Wt(i,"style",s),_s(i,n.precedence,t),e.instance=i;case"stylesheet":s=Fa(n.href);var o=t.querySelector(Wi(s));if(o)return e.state.loading|=4,e.instance=o,Zt(o),o;i=Dm(n),(s=Ce.get(s))&&ho(i,s),o=(t.ownerDocument||t).createElement("link"),Zt(o);var h=o;return h._p=new Promise(function(g,S){h.onload=g,h.onerror=S}),Wt(o,"link",i),e.state.loading|=4,_s(o,n.precedence,t),e.instance=o;case"script":return o=Wa(n.src),(s=t.querySelector($i(o)))?(e.instance=s,Zt(s),s):(i=n,(s=Ce.get(o))&&(i=v({},n),mo(i,s)),t=t.ownerDocument||t,s=t.createElement("script"),Zt(s),Wt(s,"link",i),t.head.appendChild(s),e.instance=s);case"void":return null;default:throw Error(r(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(i=e.instance,e.state.loading|=4,_s(i,n.precedence,t));return e.instance}function _s(t,e,n){for(var i=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),s=i.length?i[i.length-1]:null,o=s,h=0;h<i.length;h++){var g=i[h];if(g.dataset.precedence===e)o=g;else if(o!==s)break}o?o.parentNode.insertBefore(t,o.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function ho(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function mo(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Ns=null;function Om(t,e,n){if(Ns===null){var i=new Map,s=Ns=new Map;s.set(n,i)}else s=Ns,i=s.get(n),i||(i=new Map,s.set(n,i));if(i.has(t))return i;for(i.set(t,null),n=n.getElementsByTagName(t),s=0;s<n.length;s++){var o=n[s];if(!(o[fi]||o[It]||t==="link"&&o.getAttribute("rel")==="stylesheet")&&o.namespaceURI!=="http://www.w3.org/2000/svg"){var h=o.getAttribute(e)||"";h=t+h;var g=i.get(h);g?g.push(o):i.set(h,[o])}}return i}function Cm(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function c1(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function _m(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Ii=null;function f1(){}function h1(t,e,n){if(Ii===null)throw Error(r(475));var i=Ii;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var s=Fa(n.href),o=t.querySelector(Wi(s));if(o){t=o._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(i.count++,i=Vs.bind(i),t.then(i,i)),e.state.loading|=4,e.instance=o,Zt(o);return}o=t.ownerDocument||t,n=Dm(n),(s=Ce.get(s))&&ho(n,s),o=o.createElement("link"),Zt(o);var h=o;h._p=new Promise(function(g,S){h.onload=g,h.onerror=S}),Wt(o,"link",n),e.instance=o}i.stylesheets===null&&(i.stylesheets=new Map),i.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(i.count++,e=Vs.bind(i),t.addEventListener("load",e),t.addEventListener("error",e))}}function d1(){if(Ii===null)throw Error(r(475));var t=Ii;return t.stylesheets&&t.count===0&&po(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&po(t,t.stylesheets),t.unsuspend){var i=t.unsuspend;t.unsuspend=null,i()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function Vs(){if(this.count--,this.count===0){if(this.stylesheets)po(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var zs=null;function po(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,zs=new Map,e.forEach(m1,t),zs=null,Vs.call(t))}function m1(t,e){if(!(e.state.loading&4)){var n=zs.get(t);if(n)var i=n.get(null);else{n=new Map,zs.set(t,n);for(var s=t.querySelectorAll("link[data-precedence],style[data-precedence]"),o=0;o<s.length;o++){var h=s[o];(h.nodeName==="LINK"||h.getAttribute("media")!=="not all")&&(n.set(h.dataset.precedence,h),i=h)}i&&n.set(null,i)}s=e.instance,h=s.getAttribute("data-precedence"),o=n.get(h)||i,o===i&&n.set(null,s),n.set(h,s),this.count++,i=Vs.bind(this),s.addEventListener("load",i),s.addEventListener("error",i),o?o.parentNode.insertBefore(s,o.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(s,t.firstChild)),e.state.loading|=4}}var tl={$$typeof:q,Provider:null,Consumer:null,_currentValue:P,_currentValue2:P,_threadCount:0};function p1(t,e,n,i,s,o,h,g){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=cu(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=cu(0),this.hiddenUpdates=cu(null),this.identifierPrefix=i,this.onUncaughtError=s,this.onCaughtError=o,this.onRecoverableError=h,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=g,this.incompleteTransitions=new Map}function Nm(t,e,n,i,s,o,h,g,S,D,N,w){return t=new p1(t,e,n,h,g,S,D,w),e=1,o===!0&&(e|=24),o=pe(3,null,null,e),t.current=o,o.stateNode=t,e=Ju(),e.refCount++,t.pooledCache=e,e.refCount++,o.memoizedState={element:i,isDehydrated:n,cache:e},Iu(o),t}function Vm(t){return t?(t=Oa,t):Oa}function zm(t,e,n,i,s,o){s=Vm(s),i.context===null?i.context=s:i.pendingContext=s,i=Sn(e),i.payload={element:n},o=o===void 0?null:o,o!==null&&(i.callback=o),n=Tn(t,i,e),n!==null&&(Se(n,t,e),_i(n,t,e))}function wm(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function yo(t,e){wm(t,e),(t=t.alternate)&&wm(t,e)}function jm(t){if(t.tag===13){var e=Ra(t,67108864);e!==null&&Se(e,t,67108864),yo(t,67108864)}}var ws=!0;function y1(t,e,n,i){var s=V.T;V.T=null;var o=B.p;try{B.p=2,go(t,e,n,i)}finally{B.p=o,V.T=s}}function g1(t,e,n,i){var s=V.T;V.T=null;var o=B.p;try{B.p=8,go(t,e,n,i)}finally{B.p=o,V.T=s}}function go(t,e,n,i){if(ws){var s=vo(i);if(s===null)ao(t,e,i,js,n),Bm(t,i);else if(b1(s,t,e,n,i))i.stopPropagation();else if(Bm(t,i),e&4&&-1<v1.indexOf(t)){for(;s!==null;){var o=pa(s);if(o!==null)switch(o.tag){case 3:if(o=o.stateNode,o.current.memoizedState.isDehydrated){var h=Xn(o.pendingLanes);if(h!==0){var g=o;for(g.pendingLanes|=2,g.entangledLanes|=2;h;){var S=1<<31-de(h);g.entanglements[1]|=S,h&=~S}Qe(o),(gt&6)===0&&(vs=qe()+500,ki(0))}}break;case 13:g=Ra(o,2),g!==null&&Se(g,o,2),Ss(),yo(o,2)}if(o=vo(i),o===null&&ao(t,e,i,js,n),o===s)break;s=o}s!==null&&i.stopPropagation()}else ao(t,e,i,null,n)}}function vo(t){return t=xu(t),bo(t)}var js=null;function bo(t){if(js=null,t=ma(t),t!==null){var e=d(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=f(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return js=t,null}function Um(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ag()){case Jc:return 2;case Fc:return 8;case Rl:case ig:return 32;case Wc:return 268435456;default:return 32}default:return 32}}var So=!1,wn=null,jn=null,Un=null,el=new Map,nl=new Map,Bn=[],v1="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Bm(t,e){switch(t){case"focusin":case"focusout":wn=null;break;case"dragenter":case"dragleave":jn=null;break;case"mouseover":case"mouseout":Un=null;break;case"pointerover":case"pointerout":el.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":nl.delete(e.pointerId)}}function al(t,e,n,i,s,o){return t===null||t.nativeEvent!==o?(t={blockedOn:e,domEventName:n,eventSystemFlags:i,nativeEvent:o,targetContainers:[s]},e!==null&&(e=pa(e),e!==null&&jm(e)),t):(t.eventSystemFlags|=i,e=t.targetContainers,s!==null&&e.indexOf(s)===-1&&e.push(s),t)}function b1(t,e,n,i,s){switch(e){case"focusin":return wn=al(wn,t,e,n,i,s),!0;case"dragenter":return jn=al(jn,t,e,n,i,s),!0;case"mouseover":return Un=al(Un,t,e,n,i,s),!0;case"pointerover":var o=s.pointerId;return el.set(o,al(el.get(o)||null,t,e,n,i,s)),!0;case"gotpointercapture":return o=s.pointerId,nl.set(o,al(nl.get(o)||null,t,e,n,i,s)),!0}return!1}function Lm(t){var e=ma(t.target);if(e!==null){var n=d(e);if(n!==null){if(e=n.tag,e===13){if(e=f(n),e!==null){t.blockedOn=e,hg(t.priority,function(){if(n.tag===13){var i=be();i=fu(i);var s=Ra(n,i);s!==null&&Se(s,n,i),yo(n,i)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Us(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=vo(t.nativeEvent);if(n===null){n=t.nativeEvent;var i=new n.constructor(n.type,n);Tu=i,n.target.dispatchEvent(i),Tu=null}else return e=pa(n),e!==null&&jm(e),t.blockedOn=n,!1;e.shift()}return!0}function Hm(t,e,n){Us(t)&&n.delete(e)}function S1(){So=!1,wn!==null&&Us(wn)&&(wn=null),jn!==null&&Us(jn)&&(jn=null),Un!==null&&Us(Un)&&(Un=null),el.forEach(Hm),nl.forEach(Hm)}function Bs(t,e){t.blockedOn===e&&(t.blockedOn=null,So||(So=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,S1)))}var Ls=null;function qm(t){Ls!==t&&(Ls=t,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){Ls===t&&(Ls=null);for(var e=0;e<t.length;e+=3){var n=t[e],i=t[e+1],s=t[e+2];if(typeof i!="function"){if(bo(i||n)===null)continue;break}var o=pa(n);o!==null&&(t.splice(e,3),e-=3,vr(o,{pending:!0,data:s,method:n.method,action:i},i,s))}}))}function il(t){function e(S){return Bs(S,t)}wn!==null&&Bs(wn,t),jn!==null&&Bs(jn,t),Un!==null&&Bs(Un,t),el.forEach(e),nl.forEach(e);for(var n=0;n<Bn.length;n++){var i=Bn[n];i.blockedOn===t&&(i.blockedOn=null)}for(;0<Bn.length&&(n=Bn[0],n.blockedOn===null);)Lm(n),n.blockedOn===null&&Bn.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(i=0;i<n.length;i+=3){var s=n[i],o=n[i+1],h=s[le]||null;if(typeof o=="function")h||qm(n);else if(h){var g=null;if(o&&o.hasAttribute("formAction")){if(s=o,h=o[le]||null)g=h.formAction;else if(bo(s)!==null)continue}else g=h.action;typeof g=="function"?n[i+1]=g:(n.splice(i,3),i-=3),qm(n)}}}function To(t){this._internalRoot=t}Hs.prototype.render=To.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(r(409));var n=e.current,i=be();zm(n,i,t,e,null,null)},Hs.prototype.unmount=To.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;zm(t.current,2,null,t,null,null),Ss(),e[da]=null}};function Hs(t){this._internalRoot=t}Hs.prototype.unstable_scheduleHydration=function(t){if(t){var e=nf();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Bn.length&&e!==0&&e<Bn[n].priority;n++);Bn.splice(n,0,t),n===0&&Lm(t)}};var Ym=l.version;if(Ym!=="19.1.0")throw Error(r(527,Ym,"19.1.0"));B.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(r(188)):(t=Object.keys(t).join(","),Error(r(268,t)));return t=y(e),t=t!==null?p(t):null,t=t===null?null:t.stateNode,t};var T1={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:V,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var qs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!qs.isDisabled&&qs.supportsFiber)try{ri=qs.inject(T1),he=qs}catch{}}return sl.createRoot=function(t,e){if(!c(t))throw Error(r(299));var n=!1,i="",s=ad,o=id,h=ld,g=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(i=e.identifierPrefix),e.onUncaughtError!==void 0&&(s=e.onUncaughtError),e.onCaughtError!==void 0&&(o=e.onCaughtError),e.onRecoverableError!==void 0&&(h=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(g=e.unstable_transitionCallbacks)),e=Nm(t,1,!1,null,null,n,i,s,o,h,g,null),t[da]=e.current,no(t),new To(e)},sl.hydrateRoot=function(t,e,n){if(!c(t))throw Error(r(299));var i=!1,s="",o=ad,h=id,g=ld,S=null,D=null;return n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onUncaughtError!==void 0&&(o=n.onUncaughtError),n.onCaughtError!==void 0&&(h=n.onCaughtError),n.onRecoverableError!==void 0&&(g=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(S=n.unstable_transitionCallbacks),n.formState!==void 0&&(D=n.formState)),e=Nm(t,1,!0,e,n??null,i,s,o,h,g,S,D),e.context=Vm(null),n=e.current,i=be(),i=fu(i),s=Sn(i),s.callback=null,Tn(n,s,i),n=i,e.current.lanes=n,ci(e,n),Qe(e),t[da]=e.current,no(t),new Hs(e)},sl.version="19.1.0",sl}var Wm;function N1(){if(Wm)return Eo.exports;Wm=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(l){console.error(l)}}return a(),Eo.exports=_1(),Eo.exports}var V1=N1(),Oo={exports:{}},Co,$m;function z1(){if($m)return Co;$m=1;var a="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";return Co=a,Co}var _o,Im;function w1(){if(Im)return _o;Im=1;var a=z1();function l(){}function u(){}return u.resetWarningCache=l,_o=function(){function r(f,m,y,p,v,b){if(b!==a){var T=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw T.name="Invariant Violation",T}}r.isRequired=r;function c(){return r}var d={array:r,bigint:r,bool:r,func:r,number:r,object:r,string:r,symbol:r,any:r,arrayOf:c,element:r,elementType:r,instanceOf:c,node:r,objectOf:c,oneOf:c,oneOfType:c,shape:c,exact:c,checkPropTypes:u,resetWarningCache:l};return d.PropTypes=d,d},_o}var tp;function j1(){return tp||(tp=1,Oo.exports=w1()()),Oo.exports}var Et=j1();function yy(a,l){if(!(a instanceof l))throw new TypeError("Cannot call a class as a function")}function U1(a,l){for(var u=0;u<l.length;u++){var r=l[u];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(a,r.key,r)}}function gy(a,l,u){return l&&U1(a.prototype,l),a}function vy(a,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function");a.prototype=Object.create(l&&l.prototype,{constructor:{value:a,writable:!0,configurable:!0}}),l&&Zo(a,l)}function Fs(a){return Fs=Object.setPrototypeOf?Object.getPrototypeOf:function(u){return u.__proto__||Object.getPrototypeOf(u)},Fs(a)}function Zo(a,l){return Zo=Object.setPrototypeOf||function(r,c){return r.__proto__=c,r},Zo(a,l)}function B1(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function L1(a){if(a===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a}function H1(a,l){return l&&(typeof l=="object"||typeof l=="function")?l:L1(a)}function by(a){var l=B1();return function(){var r=Fs(a),c;if(l){var d=Fs(this).constructor;c=Reflect.construct(r,arguments,d)}else c=r.apply(this,arguments);return H1(this,c)}}function q1(a){return Y1(a)||G1(a)||X1(a)||Z1()}function Y1(a){if(Array.isArray(a))return Qo(a)}function G1(a){if(typeof Symbol<"u"&&Symbol.iterator in Object(a))return Array.from(a)}function X1(a,l){if(a){if(typeof a=="string")return Qo(a,l);var u=Object.prototype.toString.call(a).slice(8,-1);if(u==="Object"&&a.constructor&&(u=a.constructor.name),u==="Map"||u==="Set")return Array.from(a);if(u==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(u))return Qo(a,l)}}function Qo(a,l){(l==null||l>a.length)&&(l=a.length);for(var u=0,r=new Array(l);u<l;u++)r[u]=a[u];return r}function Z1(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ul(a){var l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:2,u=String(a);if(l===0)return u;var r=u.match(/(.*?)([0-9]+)(.*)/),c=r?r[1]:"",d=r?r[3]:"",f=r?r[2]:u,m=f.length>=l?f:(q1(Array(l)).map(function(){return"0"}).join("")+f).slice(l*-1);return"".concat(c).concat(m).concat(d)}var Sy={daysInHours:!1,zeroPadTime:2};function Q1(a){var l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},u=l.now,r=u===void 0?Date.now:u,c=l.precision,d=c===void 0?0:c,f=l.controlled,m=l.offsetTime,y=m===void 0?0:m,p=l.overtime,v;typeof a=="string"?v=new Date(a).getTime():a instanceof Date?v=a.getTime():v=a,f||(v+=y);var b=f?v:v-r(),T=Math.min(20,Math.max(0,d)),C=Math.round(parseFloat(((p?b:Math.max(0,b))/1e3).toFixed(T))*1e3),j=Math.abs(C)/1e3;return{total:C,days:Math.floor(j/(3600*24)),hours:Math.floor(j/3600%24),minutes:Math.floor(j/60%60),seconds:Math.floor(j%60),milliseconds:Number((j%1*1e3).toFixed()),completed:C<=0}}function K1(a,l){var u=a.days,r=a.hours,c=a.minutes,d=a.seconds,f=Object.assign(Object.assign({},Sy),l),m=f.daysInHours,y=f.zeroPadTime,p=f.zeroPadDays,v=p===void 0?y:p,b=Math.min(2,y),T=m?ul(r+u*24,y):ul(r,b);return{days:m?"":ul(u,v),hours:T,minutes:ul(c,b),seconds:ul(d,b)}}var Ty=function(a){vy(u,a);var l=by(u);function u(){var r;return yy(this,u),r=l.apply(this,arguments),r.state={count:r.props.count||3},r.startCountdown=function(){r.interval=window.setInterval(function(){var c=r.state.count-1;c===0?(r.stopCountdown(),r.props.onComplete&&r.props.onComplete()):r.setState(function(d){return{count:d.count-1}})},1e3)},r.stopCountdown=function(){clearInterval(r.interval)},r.addTime=function(c){r.stopCountdown(),r.setState(function(d){return{count:d.count+c}},r.startCountdown)},r}return gy(u,[{key:"componentDidMount",value:function(){this.startCountdown()}},{key:"componentWillUnmount",value:function(){clearInterval(this.interval)}},{key:"render",value:function(){return this.props.children?G.cloneElement(this.props.children,{count:this.state.count}):null}}]),u}(G.Component);Ty.propTypes={count:Et.number,children:Et.element,onComplete:Et.func};var dc=function(a){vy(u,a);var l=by(u);function u(r){var c;if(yy(this,u),c=l.call(this,r),c.mounted=!1,c.initialTimestamp=c.calcOffsetStartTimestamp(),c.offsetStartTimestamp=c.props.autoStart?0:c.initialTimestamp,c.offsetTime=0,c.legacyMode=!1,c.legacyCountdownRef=null,c.tick=function(){var f=c.calcTimeDelta(),m=f.completed&&!c.props.overtime?void 0:c.props.onTick;c.setTimeDeltaState(f,void 0,m)},c.setLegacyCountdownRef=function(f){c.legacyCountdownRef=f},c.start=function(){if(!c.isStarted()){var f=c.offsetStartTimestamp;c.offsetStartTimestamp=0,c.offsetTime+=f?c.calcOffsetStartTimestamp()-f:0;var m=c.calcTimeDelta();c.setTimeDeltaState(m,"STARTED",c.props.onStart),!c.props.controlled&&(!m.completed||c.props.overtime)&&(c.clearTimer(),c.interval=window.setInterval(c.tick,c.props.intervalDelay))}},c.pause=function(){c.isPaused()||(c.clearTimer(),c.offsetStartTimestamp=c.calcOffsetStartTimestamp(),c.setTimeDeltaState(c.state.timeDelta,"PAUSED",c.props.onPause))},c.stop=function(){c.isStopped()||(c.clearTimer(),c.offsetStartTimestamp=c.calcOffsetStartTimestamp(),c.offsetTime=c.offsetStartTimestamp-c.initialTimestamp,c.setTimeDeltaState(c.calcTimeDelta(),"STOPPED",c.props.onStop))},c.isStarted=function(){return c.isStatus("STARTED")},c.isPaused=function(){return c.isStatus("PAUSED")},c.isStopped=function(){return c.isStatus("STOPPED")},c.isCompleted=function(){return c.isStatus("COMPLETED")},r.date){var d=c.calcTimeDelta();c.state={timeDelta:d,status:d.completed?"COMPLETED":"STOPPED"}}else c.legacyMode=!0;return c}return gy(u,[{key:"componentDidMount",value:function(){this.legacyMode||(this.mounted=!0,this.props.onMount&&this.props.onMount(this.calcTimeDelta()),this.props.autoStart&&this.start())}},{key:"componentDidUpdate",value:function(c){this.legacyMode||this.props.date!==c.date&&(this.initialTimestamp=this.calcOffsetStartTimestamp(),this.offsetStartTimestamp=this.initialTimestamp,this.offsetTime=0,this.setTimeDeltaState(this.calcTimeDelta()))}},{key:"componentWillUnmount",value:function(){this.legacyMode||(this.mounted=!1,this.clearTimer())}},{key:"calcTimeDelta",value:function(){var c=this.props,d=c.date,f=c.now,m=c.precision,y=c.controlled,p=c.overtime;return Q1(d,{now:f,precision:m,controlled:y,offsetTime:this.offsetTime,overtime:p})}},{key:"calcOffsetStartTimestamp",value:function(){return Date.now()}},{key:"addTime",value:function(c){this.legacyCountdownRef.addTime(c)}},{key:"clearTimer",value:function(){window.clearInterval(this.interval)}},{key:"isStatus",value:function(c){return this.state.status===c}},{key:"setTimeDeltaState",value:function(c,d,f){var m=this;if(this.mounted){var y=c.completed&&!this.state.timeDelta.completed,p=c.completed&&d==="STARTED";y&&!this.props.overtime&&this.clearTimer();var v=function(){f&&f(m.state.timeDelta),m.props.onComplete&&(y||p)&&m.props.onComplete(c,p)};return this.setState(function(b){var T=d||b.status;return c.completed&&!m.props.overtime?T="COMPLETED":!d&&T==="COMPLETED"&&(T="STOPPED"),{timeDelta:c,status:T}},v)}}},{key:"getApi",value:function(){return this.api=this.api||{start:this.start,pause:this.pause,stop:this.stop,isStarted:this.isStarted,isPaused:this.isPaused,isStopped:this.isStopped,isCompleted:this.isCompleted}}},{key:"getRenderProps",value:function(){var c=this.props,d=c.daysInHours,f=c.zeroPadTime,m=c.zeroPadDays,y=this.state.timeDelta;return Object.assign(Object.assign({},y),{api:this.getApi(),props:this.props,formatted:K1(y,{daysInHours:d,zeroPadTime:f,zeroPadDays:m})})}},{key:"render",value:function(){if(this.legacyMode){var c=this.props,d=c.count,f=c.children,m=c.onComplete;return G.createElement(Ty,{ref:this.setLegacyCountdownRef,count:d,onComplete:m},f)}var y=this.props,p=y.className,v=y.overtime,b=y.children,T=y.renderer,C=this.getRenderProps();if(T)return T(C);if(b&&this.state.timeDelta.completed&&!v)return G.cloneElement(b,{countdown:C});var j=C.formatted,L=j.days,Z=j.hours,Y=j.minutes,Q=j.seconds;return G.createElement("span",{className:p},C.total<0?"-":"",L,L?":":"",Z,":",Y,":",Q)}}]),u}(G.Component);dc.defaultProps=Object.assign(Object.assign({},Sy),{controlled:!1,intervalDelay:1e3,precision:0,autoStart:!0});dc.propTypes={date:Et.oneOfType([Et.instanceOf(Date),Et.string,Et.number]),daysInHours:Et.bool,zeroPadTime:Et.number,zeroPadDays:Et.number,controlled:Et.bool,intervalDelay:Et.number,precision:Et.number,autoStart:Et.bool,overtime:Et.bool,className:Et.string,children:Et.element,renderer:Et.func,now:Et.func,onMount:Et.func,onStart:Et.func,onPause:Et.func,onStop:Et.func,onTick:Et.func,onComplete:Et.func};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const k1=a=>a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),P1=a=>a.replace(/^([A-Z])|[\s-_]+(\w)/g,(l,u,r)=>r?r.toUpperCase():u.toLowerCase()),ep=a=>{const l=P1(a);return l.charAt(0).toUpperCase()+l.slice(1)},xy=(...a)=>a.filter((l,u,r)=>!!l&&l.trim()!==""&&r.indexOf(l)===u).join(" ").trim(),J1=a=>{for(const l in a)if(l.startsWith("aria-")||l==="role"||l==="title")return!0};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var F1={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const W1=G.forwardRef(({color:a="currentColor",size:l=24,strokeWidth:u=2,absoluteStrokeWidth:r,className:c="",children:d,iconNode:f,...m},y)=>G.createElement("svg",{ref:y,...F1,width:l,height:l,stroke:a,strokeWidth:r?Number(u)*24/Number(l):u,className:xy("lucide",c),...!d&&!J1(m)&&{"aria-hidden":"true"},...m},[...f.map(([p,v])=>G.createElement(p,v)),...Array.isArray(d)?d:[d]]));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Le=(a,l)=>{const u=G.forwardRef(({className:r,...c},d)=>G.createElement(W1,{ref:d,iconNode:l,className:xy(`lucide-${k1(ep(a))}`,`lucide-${a}`,r),...c}));return u.displayName=ep(a),u};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $1=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],I1=Le("calendar",$1);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tb=[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],eb=Le("clock",tb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nb=[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]],ab=Le("facebook",nb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ib=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],lb=Le("globe",ib);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sb=[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]],ub=Le("instagram",sb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rb=[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]],ob=Le("linkedin",rb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cb=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],fb=Le("mail",cb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hb=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],db=Le("map-pin",hb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mb=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],pb=Le("menu",mb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yb=[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]],gb=Le("twitter",yb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vb=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Ay=Le("x",vb),mc=G.createContext({});function pc(a){const l=G.useRef(null);return l.current===null&&(l.current=a()),l.current}const yc=typeof window<"u",Ey=yc?G.useLayoutEffect:G.useEffect,nu=G.createContext(null);function gc(a,l){a.indexOf(l)===-1&&a.push(l)}function vc(a,l){const u=a.indexOf(l);u>-1&&a.splice(u,1)}const hn=(a,l,u)=>u>l?l:u<a?a:u;let bc=()=>{};const dn={},My=a=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(a);function Dy(a){return typeof a=="object"&&a!==null}const Ry=a=>/^0[^.\s]+$/u.test(a);function Sc(a){let l;return()=>(l===void 0&&(l=a()),l)}const Ve=a=>a,bb=(a,l)=>u=>l(a(u)),xl=(...a)=>a.reduce(bb),pl=(a,l,u)=>{const r=l-a;return r===0?1:(u-a)/r};class Tc{constructor(){this.subscriptions=[]}add(l){return gc(this.subscriptions,l),()=>vc(this.subscriptions,l)}notify(l,u,r){const c=this.subscriptions.length;if(c)if(c===1)this.subscriptions[0](l,u,r);else for(let d=0;d<c;d++){const f=this.subscriptions[d];f&&f(l,u,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Ke=a=>a*1e3,ke=a=>a/1e3;function Oy(a,l){return l?a*(1e3/l):0}const Cy=(a,l,u)=>(((1-3*u+3*l)*a+(3*u-6*l))*a+3*l)*a,Sb=1e-7,Tb=12;function xb(a,l,u,r,c){let d,f,m=0;do f=l+(u-l)/2,d=Cy(f,r,c)-a,d>0?u=f:l=f;while(Math.abs(d)>Sb&&++m<Tb);return f}function Al(a,l,u,r){if(a===l&&u===r)return Ve;const c=d=>xb(d,0,1,a,u);return d=>d===0||d===1?d:Cy(c(d),l,r)}const _y=a=>l=>l<=.5?a(2*l)/2:(2-a(2*(1-l)))/2,Ny=a=>l=>1-a(1-l),Vy=Al(.33,1.53,.69,.99),xc=Ny(Vy),zy=_y(xc),wy=a=>(a*=2)<1?.5*xc(a):.5*(2-Math.pow(2,-10*(a-1))),Ac=a=>1-Math.sin(Math.acos(a)),jy=Ny(Ac),Uy=_y(Ac),Ab=Al(.42,0,1,1),Eb=Al(0,0,.58,1),By=Al(.42,0,.58,1),Mb=a=>Array.isArray(a)&&typeof a[0]!="number",Ly=a=>Array.isArray(a)&&typeof a[0]=="number",Db={linear:Ve,easeIn:Ab,easeInOut:By,easeOut:Eb,circIn:Ac,circInOut:Uy,circOut:jy,backIn:xc,backInOut:zy,backOut:Vy,anticipate:wy},Rb=a=>typeof a=="string",np=a=>{if(Ly(a)){bc(a.length===4);const[l,u,r,c]=a;return Al(l,u,r,c)}else if(Rb(a))return Db[a];return a},Ys=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],ap={value:null};function Ob(a,l){let u=new Set,r=new Set,c=!1,d=!1;const f=new WeakSet;let m={delta:0,timestamp:0,isProcessing:!1},y=0;function p(b){f.has(b)&&(v.schedule(b),a()),y++,b(m)}const v={schedule:(b,T=!1,C=!1)=>{const L=C&&c?u:r;return T&&f.add(b),L.has(b)||L.add(b),b},cancel:b=>{r.delete(b),f.delete(b)},process:b=>{if(m=b,c){d=!0;return}c=!0,[u,r]=[r,u],u.forEach(p),l&&ap.value&&ap.value.frameloop[l].push(y),y=0,u.clear(),c=!1,d&&(d=!1,v.process(b))}};return v}const Cb=40;function Hy(a,l){let u=!1,r=!0;const c={delta:0,timestamp:0,isProcessing:!1},d=()=>u=!0,f=Ys.reduce((q,tt)=>(q[tt]=Ob(d,l?tt:void 0),q),{}),{setup:m,read:y,resolveKeyframes:p,preUpdate:v,update:b,preRender:T,render:C,postRender:j}=f,L=()=>{const q=dn.useManualTiming?c.timestamp:performance.now();u=!1,dn.useManualTiming||(c.delta=r?1e3/60:Math.max(Math.min(q-c.timestamp,Cb),1)),c.timestamp=q,c.isProcessing=!0,m.process(c),y.process(c),p.process(c),v.process(c),b.process(c),T.process(c),C.process(c),j.process(c),c.isProcessing=!1,u&&l&&(r=!1,a(L))},Z=()=>{u=!0,r=!0,c.isProcessing||a(L)};return{schedule:Ys.reduce((q,tt)=>{const H=f[tt];return q[tt]=(W,nt=!1,J=!1)=>(u||Z(),H.schedule(W,nt,J)),q},{}),cancel:q=>{for(let tt=0;tt<Ys.length;tt++)f[Ys[tt]].cancel(q)},state:c,steps:f}}const{schedule:Rt,cancel:qn,state:$t,steps:No}=Hy(typeof requestAnimationFrame<"u"?requestAnimationFrame:Ve,!0);let Qs;function _b(){Qs=void 0}const ce={now:()=>(Qs===void 0&&ce.set($t.isProcessing||dn.useManualTiming?$t.timestamp:performance.now()),Qs),set:a=>{Qs=a,queueMicrotask(_b)}},qy=a=>l=>typeof l=="string"&&l.startsWith(a),Ec=qy("--"),Nb=qy("var(--"),Mc=a=>Nb(a)?Vb.test(a.split("/*")[0].trim()):!1,Vb=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,li={test:a=>typeof a=="number",parse:parseFloat,transform:a=>a},yl={...li,transform:a=>hn(0,1,a)},Gs={...li,default:1},cl=a=>Math.round(a*1e5)/1e5,Dc=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function zb(a){return a==null}const wb=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Rc=(a,l)=>u=>!!(typeof u=="string"&&wb.test(u)&&u.startsWith(a)||l&&!zb(u)&&Object.prototype.hasOwnProperty.call(u,l)),Yy=(a,l,u)=>r=>{if(typeof r!="string")return r;const[c,d,f,m]=r.match(Dc);return{[a]:parseFloat(c),[l]:parseFloat(d),[u]:parseFloat(f),alpha:m!==void 0?parseFloat(m):1}},jb=a=>hn(0,255,a),Vo={...li,transform:a=>Math.round(jb(a))},oa={test:Rc("rgb","red"),parse:Yy("red","green","blue"),transform:({red:a,green:l,blue:u,alpha:r=1})=>"rgba("+Vo.transform(a)+", "+Vo.transform(l)+", "+Vo.transform(u)+", "+cl(yl.transform(r))+")"};function Ub(a){let l="",u="",r="",c="";return a.length>5?(l=a.substring(1,3),u=a.substring(3,5),r=a.substring(5,7),c=a.substring(7,9)):(l=a.substring(1,2),u=a.substring(2,3),r=a.substring(3,4),c=a.substring(4,5),l+=l,u+=u,r+=r,c+=c),{red:parseInt(l,16),green:parseInt(u,16),blue:parseInt(r,16),alpha:c?parseInt(c,16)/255:1}}const Ko={test:Rc("#"),parse:Ub,transform:oa.transform},El=a=>({test:l=>typeof l=="string"&&l.endsWith(a)&&l.split(" ").length===1,parse:parseFloat,transform:l=>`${l}${a}`}),Hn=El("deg"),Pe=El("%"),it=El("px"),Bb=El("vh"),Lb=El("vw"),ip={...Pe,parse:a=>Pe.parse(a)/100,transform:a=>Pe.transform(a*100)},$a={test:Rc("hsl","hue"),parse:Yy("hue","saturation","lightness"),transform:({hue:a,saturation:l,lightness:u,alpha:r=1})=>"hsla("+Math.round(a)+", "+Pe.transform(cl(l))+", "+Pe.transform(cl(u))+", "+cl(yl.transform(r))+")"},Ht={test:a=>oa.test(a)||Ko.test(a)||$a.test(a),parse:a=>oa.test(a)?oa.parse(a):$a.test(a)?$a.parse(a):Ko.parse(a),transform:a=>typeof a=="string"?a:a.hasOwnProperty("red")?oa.transform(a):$a.transform(a),getAnimatableNone:a=>{const l=Ht.parse(a);return l.alpha=0,Ht.transform(l)}},Hb=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function qb(a){return isNaN(a)&&typeof a=="string"&&(a.match(Dc)?.length||0)+(a.match(Hb)?.length||0)>0}const Gy="number",Xy="color",Yb="var",Gb="var(",lp="${}",Xb=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function gl(a){const l=a.toString(),u=[],r={color:[],number:[],var:[]},c=[];let d=0;const m=l.replace(Xb,y=>(Ht.test(y)?(r.color.push(d),c.push(Xy),u.push(Ht.parse(y))):y.startsWith(Gb)?(r.var.push(d),c.push(Yb),u.push(y)):(r.number.push(d),c.push(Gy),u.push(parseFloat(y))),++d,lp)).split(lp);return{values:u,split:m,indexes:r,types:c}}function Zy(a){return gl(a).values}function Qy(a){const{split:l,types:u}=gl(a),r=l.length;return c=>{let d="";for(let f=0;f<r;f++)if(d+=l[f],c[f]!==void 0){const m=u[f];m===Gy?d+=cl(c[f]):m===Xy?d+=Ht.transform(c[f]):d+=c[f]}return d}}const Zb=a=>typeof a=="number"?0:Ht.test(a)?Ht.getAnimatableNone(a):a;function Qb(a){const l=Zy(a);return Qy(a)(l.map(Zb))}const Yn={test:qb,parse:Zy,createTransformer:Qy,getAnimatableNone:Qb};function zo(a,l,u){return u<0&&(u+=1),u>1&&(u-=1),u<1/6?a+(l-a)*6*u:u<1/2?l:u<2/3?a+(l-a)*(2/3-u)*6:a}function Kb({hue:a,saturation:l,lightness:u,alpha:r}){a/=360,l/=100,u/=100;let c=0,d=0,f=0;if(!l)c=d=f=u;else{const m=u<.5?u*(1+l):u+l-u*l,y=2*u-m;c=zo(y,m,a+1/3),d=zo(y,m,a),f=zo(y,m,a-1/3)}return{red:Math.round(c*255),green:Math.round(d*255),blue:Math.round(f*255),alpha:r}}function Ws(a,l){return u=>u>0?l:a}const Ct=(a,l,u)=>a+(l-a)*u,wo=(a,l,u)=>{const r=a*a,c=u*(l*l-r)+r;return c<0?0:Math.sqrt(c)},kb=[Ko,oa,$a],Pb=a=>kb.find(l=>l.test(a));function sp(a){const l=Pb(a);if(!l)return!1;let u=l.parse(a);return l===$a&&(u=Kb(u)),u}const up=(a,l)=>{const u=sp(a),r=sp(l);if(!u||!r)return Ws(a,l);const c={...u};return d=>(c.red=wo(u.red,r.red,d),c.green=wo(u.green,r.green,d),c.blue=wo(u.blue,r.blue,d),c.alpha=Ct(u.alpha,r.alpha,d),oa.transform(c))},ko=new Set(["none","hidden"]);function Jb(a,l){return ko.has(a)?u=>u<=0?a:l:u=>u>=1?l:a}function Fb(a,l){return u=>Ct(a,l,u)}function Oc(a){return typeof a=="number"?Fb:typeof a=="string"?Mc(a)?Ws:Ht.test(a)?up:Ib:Array.isArray(a)?Ky:typeof a=="object"?Ht.test(a)?up:Wb:Ws}function Ky(a,l){const u=[...a],r=u.length,c=a.map((d,f)=>Oc(d)(d,l[f]));return d=>{for(let f=0;f<r;f++)u[f]=c[f](d);return u}}function Wb(a,l){const u={...a,...l},r={};for(const c in u)a[c]!==void 0&&l[c]!==void 0&&(r[c]=Oc(a[c])(a[c],l[c]));return c=>{for(const d in r)u[d]=r[d](c);return u}}function $b(a,l){const u=[],r={color:0,var:0,number:0};for(let c=0;c<l.values.length;c++){const d=l.types[c],f=a.indexes[d][r[d]],m=a.values[f]??0;u[c]=m,r[d]++}return u}const Ib=(a,l)=>{const u=Yn.createTransformer(l),r=gl(a),c=gl(l);return r.indexes.var.length===c.indexes.var.length&&r.indexes.color.length===c.indexes.color.length&&r.indexes.number.length>=c.indexes.number.length?ko.has(a)&&!c.values.length||ko.has(l)&&!r.values.length?Jb(a,l):xl(Ky($b(r,c),c.values),u):Ws(a,l)};function ky(a,l,u){return typeof a=="number"&&typeof l=="number"&&typeof u=="number"?Ct(a,l,u):Oc(a)(a,l)}const tS=a=>{const l=({timestamp:u})=>a(u);return{start:(u=!0)=>Rt.update(l,u),stop:()=>qn(l),now:()=>$t.isProcessing?$t.timestamp:ce.now()}},Py=(a,l,u=10)=>{let r="";const c=Math.max(Math.round(l/u),2);for(let d=0;d<c;d++)r+=Math.round(a(d/(c-1))*1e4)/1e4+", ";return`linear(${r.substring(0,r.length-2)})`},$s=2e4;function Cc(a){let l=0;const u=50;let r=a.next(l);for(;!r.done&&l<$s;)l+=u,r=a.next(l);return l>=$s?1/0:l}function eS(a,l=100,u){const r=u({...a,keyframes:[0,l]}),c=Math.min(Cc(r),$s);return{type:"keyframes",ease:d=>r.next(c*d).value/l,duration:ke(c)}}const nS=5;function Jy(a,l,u){const r=Math.max(l-nS,0);return Oy(u-a(r),l-r)}const zt={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},jo=.001;function aS({duration:a=zt.duration,bounce:l=zt.bounce,velocity:u=zt.velocity,mass:r=zt.mass}){let c,d,f=1-l;f=hn(zt.minDamping,zt.maxDamping,f),a=hn(zt.minDuration,zt.maxDuration,ke(a)),f<1?(c=p=>{const v=p*f,b=v*a,T=v-u,C=Po(p,f),j=Math.exp(-b);return jo-T/C*j},d=p=>{const b=p*f*a,T=b*u+u,C=Math.pow(f,2)*Math.pow(p,2)*a,j=Math.exp(-b),L=Po(Math.pow(p,2),f);return(-c(p)+jo>0?-1:1)*((T-C)*j)/L}):(c=p=>{const v=Math.exp(-p*a),b=(p-u)*a+1;return-jo+v*b},d=p=>{const v=Math.exp(-p*a),b=(u-p)*(a*a);return v*b});const m=5/a,y=lS(c,d,m);if(a=Ke(a),isNaN(y))return{stiffness:zt.stiffness,damping:zt.damping,duration:a};{const p=Math.pow(y,2)*r;return{stiffness:p,damping:f*2*Math.sqrt(r*p),duration:a}}}const iS=12;function lS(a,l,u){let r=u;for(let c=1;c<iS;c++)r=r-a(r)/l(r);return r}function Po(a,l){return a*Math.sqrt(1-l*l)}const sS=["duration","bounce"],uS=["stiffness","damping","mass"];function rp(a,l){return l.some(u=>a[u]!==void 0)}function rS(a){let l={velocity:zt.velocity,stiffness:zt.stiffness,damping:zt.damping,mass:zt.mass,isResolvedFromDuration:!1,...a};if(!rp(a,uS)&&rp(a,sS))if(a.visualDuration){const u=a.visualDuration,r=2*Math.PI/(u*1.2),c=r*r,d=2*hn(.05,1,1-(a.bounce||0))*Math.sqrt(c);l={...l,mass:zt.mass,stiffness:c,damping:d}}else{const u=aS(a);l={...l,...u,mass:zt.mass},l.isResolvedFromDuration=!0}return l}function Is(a=zt.visualDuration,l=zt.bounce){const u=typeof a!="object"?{visualDuration:a,keyframes:[0,1],bounce:l}:a;let{restSpeed:r,restDelta:c}=u;const d=u.keyframes[0],f=u.keyframes[u.keyframes.length-1],m={done:!1,value:d},{stiffness:y,damping:p,mass:v,duration:b,velocity:T,isResolvedFromDuration:C}=rS({...u,velocity:-ke(u.velocity||0)}),j=T||0,L=p/(2*Math.sqrt(y*v)),Z=f-d,Y=ke(Math.sqrt(y/v)),Q=Math.abs(Z)<5;r||(r=Q?zt.restSpeed.granular:zt.restSpeed.default),c||(c=Q?zt.restDelta.granular:zt.restDelta.default);let q;if(L<1){const H=Po(Y,L);q=W=>{const nt=Math.exp(-L*Y*W);return f-nt*((j+L*Y*Z)/H*Math.sin(H*W)+Z*Math.cos(H*W))}}else if(L===1)q=H=>f-Math.exp(-Y*H)*(Z+(j+Y*Z)*H);else{const H=Y*Math.sqrt(L*L-1);q=W=>{const nt=Math.exp(-L*Y*W),J=Math.min(H*W,300);return f-nt*((j+L*Y*Z)*Math.sinh(J)+H*Z*Math.cosh(J))/H}}const tt={calculatedDuration:C&&b||null,next:H=>{const W=q(H);if(C)m.done=H>=b;else{let nt=H===0?j:0;L<1&&(nt=H===0?Ke(j):Jy(q,H,W));const J=Math.abs(nt)<=r,yt=Math.abs(f-W)<=c;m.done=J&&yt}return m.value=m.done?f:W,m},toString:()=>{const H=Math.min(Cc(tt),$s),W=Py(nt=>tt.next(H*nt).value,H,30);return H+"ms "+W},toTransition:()=>{}};return tt}Is.applyToOptions=a=>{const l=eS(a,100,Is);return a.ease=l.ease,a.duration=Ke(l.duration),a.type="keyframes",a};function Jo({keyframes:a,velocity:l=0,power:u=.8,timeConstant:r=325,bounceDamping:c=10,bounceStiffness:d=500,modifyTarget:f,min:m,max:y,restDelta:p=.5,restSpeed:v}){const b=a[0],T={done:!1,value:b},C=J=>m!==void 0&&J<m||y!==void 0&&J>y,j=J=>m===void 0?y:y===void 0||Math.abs(m-J)<Math.abs(y-J)?m:y;let L=u*l;const Z=b+L,Y=f===void 0?Z:f(Z);Y!==Z&&(L=Y-b);const Q=J=>-L*Math.exp(-J/r),q=J=>Y+Q(J),tt=J=>{const yt=Q(J),_t=q(J);T.done=Math.abs(yt)<=p,T.value=T.done?Y:_t};let H,W;const nt=J=>{C(T.value)&&(H=J,W=Is({keyframes:[T.value,j(T.value)],velocity:Jy(q,J,T.value),damping:c,stiffness:d,restDelta:p,restSpeed:v}))};return nt(0),{calculatedDuration:null,next:J=>{let yt=!1;return!W&&H===void 0&&(yt=!0,tt(J),nt(J)),H!==void 0&&J>=H?W.next(J-H):(!yt&&tt(J),T)}}}function oS(a,l,u){const r=[],c=u||dn.mix||ky,d=a.length-1;for(let f=0;f<d;f++){let m=c(a[f],a[f+1]);if(l){const y=Array.isArray(l)?l[f]||Ve:l;m=xl(y,m)}r.push(m)}return r}function cS(a,l,{clamp:u=!0,ease:r,mixer:c}={}){const d=a.length;if(bc(d===l.length),d===1)return()=>l[0];if(d===2&&l[0]===l[1])return()=>l[1];const f=a[0]===a[1];a[0]>a[d-1]&&(a=[...a].reverse(),l=[...l].reverse());const m=oS(l,r,c),y=m.length,p=v=>{if(f&&v<a[0])return l[0];let b=0;if(y>1)for(;b<a.length-2&&!(v<a[b+1]);b++);const T=pl(a[b],a[b+1],v);return m[b](T)};return u?v=>p(hn(a[0],a[d-1],v)):p}function fS(a,l){const u=a[a.length-1];for(let r=1;r<=l;r++){const c=pl(0,l,r);a.push(Ct(u,1,c))}}function hS(a){const l=[0];return fS(l,a.length-1),l}function dS(a,l){return a.map(u=>u*l)}function mS(a,l){return a.map(()=>l||By).splice(0,a.length-1)}function fl({duration:a=300,keyframes:l,times:u,ease:r="easeInOut"}){const c=Mb(r)?r.map(np):np(r),d={done:!1,value:l[0]},f=dS(u&&u.length===l.length?u:hS(l),a),m=cS(f,l,{ease:Array.isArray(c)?c:mS(l,c)});return{calculatedDuration:a,next:y=>(d.value=m(y),d.done=y>=a,d)}}const pS=a=>a!==null;function _c(a,{repeat:l,repeatType:u="loop"},r,c=1){const d=a.filter(pS),m=c<0||l&&u!=="loop"&&l%2===1?0:d.length-1;return!m||r===void 0?d[m]:r}const yS={decay:Jo,inertia:Jo,tween:fl,keyframes:fl,spring:Is};function Fy(a){typeof a.type=="string"&&(a.type=yS[a.type])}class Nc{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(l=>{this.resolve=l})}notifyFinished(){this.resolve()}then(l,u){return this.finished.then(l,u)}}const gS=a=>a/100;class Vc extends Nc{constructor(l){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:u}=this.options;u&&u.updatedAt!==ce.now()&&this.tick(ce.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),this.options.onStop?.())},this.options=l,this.initAnimation(),this.play(),l.autoplay===!1&&this.pause()}initAnimation(){const{options:l}=this;Fy(l);const{type:u=fl,repeat:r=0,repeatDelay:c=0,repeatType:d,velocity:f=0}=l;let{keyframes:m}=l;const y=u||fl;y!==fl&&typeof m[0]!="number"&&(this.mixKeyframes=xl(gS,ky(m[0],m[1])),m=[0,100]);const p=y({...l,keyframes:m});d==="mirror"&&(this.mirroredGenerator=y({...l,keyframes:[...m].reverse(),velocity:-f})),p.calculatedDuration===null&&(p.calculatedDuration=Cc(p));const{calculatedDuration:v}=p;this.calculatedDuration=v,this.resolvedDuration=v+c,this.totalDuration=this.resolvedDuration*(r+1)-c,this.generator=p}updateTime(l){const u=Math.round(l-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=u}tick(l,u=!1){const{generator:r,totalDuration:c,mixKeyframes:d,mirroredGenerator:f,resolvedDuration:m,calculatedDuration:y}=this;if(this.startTime===null)return r.next(0);const{delay:p=0,keyframes:v,repeat:b,repeatType:T,repeatDelay:C,type:j,onUpdate:L,finalKeyframe:Z}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,l):this.speed<0&&(this.startTime=Math.min(l-c/this.speed,this.startTime)),u?this.currentTime=l:this.updateTime(l);const Y=this.currentTime-p*(this.playbackSpeed>=0?1:-1),Q=this.playbackSpeed>=0?Y<0:Y>c;this.currentTime=Math.max(Y,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let q=this.currentTime,tt=r;if(b){const J=Math.min(this.currentTime,c)/m;let yt=Math.floor(J),_t=J%1;!_t&&J>=1&&(_t=1),_t===1&&yt--,yt=Math.min(yt,b+1),!!(yt%2)&&(T==="reverse"?(_t=1-_t,C&&(_t-=C/m)):T==="mirror"&&(tt=f)),q=hn(0,1,_t)*m}const H=Q?{done:!1,value:v[0]}:tt.next(q);d&&(H.value=d(H.value));let{done:W}=H;!Q&&y!==null&&(W=this.playbackSpeed>=0?this.currentTime>=c:this.currentTime<=0);const nt=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&W);return nt&&j!==Jo&&(H.value=_c(v,this.options,Z,this.speed)),L&&L(H.value),nt&&this.finish(),H}then(l,u){return this.finished.then(l,u)}get duration(){return ke(this.calculatedDuration)}get time(){return ke(this.currentTime)}set time(l){l=Ke(l),this.currentTime=l,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=l:this.driver&&(this.startTime=this.driver.now()-l/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(l){this.updateTime(ce.now());const u=this.playbackSpeed!==l;this.playbackSpeed=l,u&&(this.time=ke(this.currentTime))}play(){if(this.isStopped)return;const{driver:l=tS,startTime:u}=this.options;this.driver||(this.driver=l(c=>this.tick(c))),this.options.onPlay?.();const r=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=r):this.holdTime!==null?this.startTime=r-this.holdTime:this.startTime||(this.startTime=u??r),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(ce.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(l){return this.startTime=0,this.tick(l,!0)}attachTimeline(l){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),l.observe(this)}}function vS(a){for(let l=1;l<a.length;l++)a[l]??(a[l]=a[l-1])}const ca=a=>a*180/Math.PI,Fo=a=>{const l=ca(Math.atan2(a[1],a[0]));return Wo(l)},bS={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:a=>(Math.abs(a[0])+Math.abs(a[3]))/2,rotate:Fo,rotateZ:Fo,skewX:a=>ca(Math.atan(a[1])),skewY:a=>ca(Math.atan(a[2])),skew:a=>(Math.abs(a[1])+Math.abs(a[2]))/2},Wo=a=>(a=a%360,a<0&&(a+=360),a),op=Fo,cp=a=>Math.sqrt(a[0]*a[0]+a[1]*a[1]),fp=a=>Math.sqrt(a[4]*a[4]+a[5]*a[5]),SS={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:cp,scaleY:fp,scale:a=>(cp(a)+fp(a))/2,rotateX:a=>Wo(ca(Math.atan2(a[6],a[5]))),rotateY:a=>Wo(ca(Math.atan2(-a[2],a[0]))),rotateZ:op,rotate:op,skewX:a=>ca(Math.atan(a[4])),skewY:a=>ca(Math.atan(a[1])),skew:a=>(Math.abs(a[1])+Math.abs(a[4]))/2};function $o(a){return a.includes("scale")?1:0}function Io(a,l){if(!a||a==="none")return $o(l);const u=a.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let r,c;if(u)r=SS,c=u;else{const m=a.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=bS,c=m}if(!c)return $o(l);const d=r[l],f=c[1].split(",").map(xS);return typeof d=="function"?d(f):f[d]}const TS=(a,l)=>{const{transform:u="none"}=getComputedStyle(a);return Io(u,l)};function xS(a){return parseFloat(a.trim())}const si=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],ui=new Set(si),hp=a=>a===li||a===it,AS=new Set(["x","y","z"]),ES=si.filter(a=>!AS.has(a));function MS(a){const l=[];return ES.forEach(u=>{const r=a.getValue(u);r!==void 0&&(l.push([u,r.get()]),r.set(u.startsWith("scale")?1:0))}),l}const fa={width:({x:a},{paddingLeft:l="0",paddingRight:u="0"})=>a.max-a.min-parseFloat(l)-parseFloat(u),height:({y:a},{paddingTop:l="0",paddingBottom:u="0"})=>a.max-a.min-parseFloat(l)-parseFloat(u),top:(a,{top:l})=>parseFloat(l),left:(a,{left:l})=>parseFloat(l),bottom:({y:a},{top:l})=>parseFloat(l)+(a.max-a.min),right:({x:a},{left:l})=>parseFloat(l)+(a.max-a.min),x:(a,{transform:l})=>Io(l,"x"),y:(a,{transform:l})=>Io(l,"y")};fa.translateX=fa.x;fa.translateY=fa.y;const ha=new Set;let tc=!1,ec=!1,nc=!1;function Wy(){if(ec){const a=Array.from(ha).filter(r=>r.needsMeasurement),l=new Set(a.map(r=>r.element)),u=new Map;l.forEach(r=>{const c=MS(r);c.length&&(u.set(r,c),r.render())}),a.forEach(r=>r.measureInitialState()),l.forEach(r=>{r.render();const c=u.get(r);c&&c.forEach(([d,f])=>{r.getValue(d)?.set(f)})}),a.forEach(r=>r.measureEndState()),a.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}ec=!1,tc=!1,ha.forEach(a=>a.complete(nc)),ha.clear()}function $y(){ha.forEach(a=>{a.readKeyframes(),a.needsMeasurement&&(ec=!0)})}function DS(){nc=!0,$y(),Wy(),nc=!1}class zc{constructor(l,u,r,c,d,f=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...l],this.onComplete=u,this.name=r,this.motionValue=c,this.element=d,this.isAsync=f}scheduleResolve(){this.state="scheduled",this.isAsync?(ha.add(this),tc||(tc=!0,Rt.read($y),Rt.resolveKeyframes(Wy))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:l,name:u,element:r,motionValue:c}=this;if(l[0]===null){const d=c?.get(),f=l[l.length-1];if(d!==void 0)l[0]=d;else if(r&&u){const m=r.readValue(u,f);m!=null&&(l[0]=m)}l[0]===void 0&&(l[0]=f),c&&d===void 0&&c.set(l[0])}vS(l)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(l=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,l),ha.delete(this)}cancel(){this.state==="scheduled"&&(ha.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const RS=a=>a.startsWith("--");function OS(a,l,u){RS(l)?a.style.setProperty(l,u):a.style[l]=u}const CS=Sc(()=>window.ScrollTimeline!==void 0),_S={};function NS(a,l){const u=Sc(a);return()=>_S[l]??u()}const Iy=NS(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),ol=([a,l,u,r])=>`cubic-bezier(${a}, ${l}, ${u}, ${r})`,dp={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ol([0,.65,.55,1]),circOut:ol([.55,0,1,.45]),backIn:ol([.31,.01,.66,-.59]),backOut:ol([.33,1.53,.69,.99])};function t0(a,l){if(a)return typeof a=="function"?Iy()?Py(a,l):"ease-out":Ly(a)?ol(a):Array.isArray(a)?a.map(u=>t0(u,l)||dp.easeOut):dp[a]}function VS(a,l,u,{delay:r=0,duration:c=300,repeat:d=0,repeatType:f="loop",ease:m="easeOut",times:y}={},p=void 0){const v={[l]:u};y&&(v.offset=y);const b=t0(m,c);Array.isArray(b)&&(v.easing=b);const T={delay:r,duration:c,easing:Array.isArray(b)?"linear":b,fill:"both",iterations:d+1,direction:f==="reverse"?"alternate":"normal"};return p&&(T.pseudoElement=p),a.animate(v,T)}function e0(a){return typeof a=="function"&&"applyToOptions"in a}function zS({type:a,...l}){return e0(a)&&Iy()?a.applyToOptions(l):(l.duration??(l.duration=300),l.ease??(l.ease="easeOut"),l)}class wS extends Nc{constructor(l){if(super(),this.finishedTime=null,this.isStopped=!1,!l)return;const{element:u,name:r,keyframes:c,pseudoElement:d,allowFlatten:f=!1,finalKeyframe:m,onComplete:y}=l;this.isPseudoElement=!!d,this.allowFlatten=f,this.options=l,bc(typeof l.type!="string");const p=zS(l);this.animation=VS(u,r,c,p,d),p.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!d){const v=_c(c,this.options,m,this.speed);this.updateMotionValue?this.updateMotionValue(v):OS(u,r,v),this.animation.cancel()}y?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:l}=this;l==="idle"||l==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const l=this.animation.effect?.getComputedTiming?.().duration||0;return ke(Number(l))}get time(){return ke(Number(this.animation.currentTime)||0)}set time(l){this.finishedTime=null,this.animation.currentTime=Ke(l)}get speed(){return this.animation.playbackRate}set speed(l){l<0&&(this.finishedTime=null),this.animation.playbackRate=l}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(l){this.animation.startTime=l}attachTimeline({timeline:l,observe:u}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,l&&CS()?(this.animation.timeline=l,Ve):u(this)}}const n0={anticipate:wy,backInOut:zy,circInOut:Uy};function jS(a){return a in n0}function US(a){typeof a.ease=="string"&&jS(a.ease)&&(a.ease=n0[a.ease])}const mp=10;class BS extends wS{constructor(l){US(l),Fy(l),super(l),l.startTime&&(this.startTime=l.startTime),this.options=l}updateMotionValue(l){const{motionValue:u,onUpdate:r,onComplete:c,element:d,...f}=this.options;if(!u)return;if(l!==void 0){u.set(l);return}const m=new Vc({...f,autoplay:!1}),y=Ke(this.finishedTime??this.time);u.setWithVelocity(m.sample(y-mp).value,m.sample(y).value,mp),m.stop()}}const pp=(a,l)=>l==="zIndex"?!1:!!(typeof a=="number"||Array.isArray(a)||typeof a=="string"&&(Yn.test(a)||a==="0")&&!a.startsWith("url("));function LS(a){const l=a[0];if(a.length===1)return!0;for(let u=0;u<a.length;u++)if(a[u]!==l)return!0}function HS(a,l,u,r){const c=a[0];if(c===null)return!1;if(l==="display"||l==="visibility")return!0;const d=a[a.length-1],f=pp(c,l),m=pp(d,l);return!f||!m?!1:LS(a)||(u==="spring"||e0(u))&&r}function wc(a){return Dy(a)&&"offsetHeight"in a}const qS=new Set(["opacity","clipPath","filter","transform"]),YS=Sc(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function GS(a){const{motionValue:l,name:u,repeatDelay:r,repeatType:c,damping:d,type:f}=a;if(!wc(l?.owner?.current))return!1;const{onUpdate:m,transformTemplate:y}=l.owner.getProps();return YS()&&u&&qS.has(u)&&(u!=="transform"||!y)&&!m&&!r&&c!=="mirror"&&d!==0&&f!=="inertia"}const XS=40;class ZS extends Nc{constructor({autoplay:l=!0,delay:u=0,type:r="keyframes",repeat:c=0,repeatDelay:d=0,repeatType:f="loop",keyframes:m,name:y,motionValue:p,element:v,...b}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=ce.now();const T={autoplay:l,delay:u,type:r,repeat:c,repeatDelay:d,repeatType:f,name:y,motionValue:p,element:v,...b},C=v?.KeyframeResolver||zc;this.keyframeResolver=new C(m,(j,L,Z)=>this.onKeyframesResolved(j,L,T,!Z),y,p,v),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(l,u,r,c){this.keyframeResolver=void 0;const{name:d,type:f,velocity:m,delay:y,isHandoff:p,onUpdate:v}=r;this.resolvedAt=ce.now(),HS(l,d,f,m)||((dn.instantAnimations||!y)&&v?.(_c(l,r,u)),l[0]=l[l.length-1],r.duration=0,r.repeat=0);const T={startTime:c?this.resolvedAt?this.resolvedAt-this.createdAt>XS?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:u,...r,keyframes:l},C=!p&&GS(T)?new BS({...T,element:T.motionValue.owner.current}):new Vc(T);C.finished.then(()=>this.notifyFinished()).catch(Ve),this.pendingTimeline&&(this.stopTimeline=C.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=C}get finished(){return this._animation?this.animation.finished:this._finished}then(l,u){return this.finished.finally(l).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),DS()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(l){this.animation.time=l}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(l){this.animation.speed=l}get startTime(){return this.animation.startTime}attachTimeline(l){return this._animation?this.stopTimeline=this.animation.attachTimeline(l):this.pendingTimeline=l,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const QS=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function KS(a){const l=QS.exec(a);if(!l)return[,];const[,u,r,c]=l;return[`--${u??r}`,c]}function a0(a,l,u=1){const[r,c]=KS(a);if(!r)return;const d=window.getComputedStyle(l).getPropertyValue(r);if(d){const f=d.trim();return My(f)?parseFloat(f):f}return Mc(c)?a0(c,l,u+1):c}function jc(a,l){return a?.[l]??a?.default??a}const i0=new Set(["width","height","top","left","right","bottom",...si]),kS={test:a=>a==="auto",parse:a=>a},l0=a=>l=>l.test(a),s0=[li,it,Pe,Hn,Lb,Bb,kS],yp=a=>s0.find(l0(a));function PS(a){return typeof a=="number"?a===0:a!==null?a==="none"||a==="0"||Ry(a):!0}const JS=new Set(["brightness","contrast","saturate","opacity"]);function FS(a){const[l,u]=a.slice(0,-1).split("(");if(l==="drop-shadow")return a;const[r]=u.match(Dc)||[];if(!r)return a;const c=u.replace(r,"");let d=JS.has(l)?1:0;return r!==u&&(d*=100),l+"("+d+c+")"}const WS=/\b([a-z-]*)\(.*?\)/gu,ac={...Yn,getAnimatableNone:a=>{const l=a.match(WS);return l?l.map(FS).join(" "):a}},gp={...li,transform:Math.round},$S={rotate:Hn,rotateX:Hn,rotateY:Hn,rotateZ:Hn,scale:Gs,scaleX:Gs,scaleY:Gs,scaleZ:Gs,skew:Hn,skewX:Hn,skewY:Hn,distance:it,translateX:it,translateY:it,translateZ:it,x:it,y:it,z:it,perspective:it,transformPerspective:it,opacity:yl,originX:ip,originY:ip,originZ:it},Uc={borderWidth:it,borderTopWidth:it,borderRightWidth:it,borderBottomWidth:it,borderLeftWidth:it,borderRadius:it,radius:it,borderTopLeftRadius:it,borderTopRightRadius:it,borderBottomRightRadius:it,borderBottomLeftRadius:it,width:it,maxWidth:it,height:it,maxHeight:it,top:it,right:it,bottom:it,left:it,padding:it,paddingTop:it,paddingRight:it,paddingBottom:it,paddingLeft:it,margin:it,marginTop:it,marginRight:it,marginBottom:it,marginLeft:it,backgroundPositionX:it,backgroundPositionY:it,...$S,zIndex:gp,fillOpacity:yl,strokeOpacity:yl,numOctaves:gp},IS={...Uc,color:Ht,backgroundColor:Ht,outlineColor:Ht,fill:Ht,stroke:Ht,borderColor:Ht,borderTopColor:Ht,borderRightColor:Ht,borderBottomColor:Ht,borderLeftColor:Ht,filter:ac,WebkitFilter:ac},u0=a=>IS[a];function r0(a,l){let u=u0(a);return u!==ac&&(u=Yn),u.getAnimatableNone?u.getAnimatableNone(l):void 0}const tT=new Set(["auto","none","0"]);function eT(a,l,u){let r=0,c;for(;r<a.length&&!c;){const d=a[r];typeof d=="string"&&!tT.has(d)&&gl(d).values.length&&(c=a[r]),r++}if(c&&u)for(const d of l)a[d]=r0(u,c)}class nT extends zc{constructor(l,u,r,c,d){super(l,u,r,c,d,!0)}readKeyframes(){const{unresolvedKeyframes:l,element:u,name:r}=this;if(!u||!u.current)return;super.readKeyframes();for(let y=0;y<l.length;y++){let p=l[y];if(typeof p=="string"&&(p=p.trim(),Mc(p))){const v=a0(p,u.current);v!==void 0&&(l[y]=v),y===l.length-1&&(this.finalKeyframe=p)}}if(this.resolveNoneKeyframes(),!i0.has(r)||l.length!==2)return;const[c,d]=l,f=yp(c),m=yp(d);if(f!==m)if(hp(f)&&hp(m))for(let y=0;y<l.length;y++){const p=l[y];typeof p=="string"&&(l[y]=parseFloat(p))}else fa[r]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:l,name:u}=this,r=[];for(let c=0;c<l.length;c++)(l[c]===null||PS(l[c]))&&r.push(c);r.length&&eT(l,r,u)}measureInitialState(){const{element:l,unresolvedKeyframes:u,name:r}=this;if(!l||!l.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=fa[r](l.measureViewportBox(),window.getComputedStyle(l.current)),u[0]=this.measuredOrigin;const c=u[u.length-1];c!==void 0&&l.getValue(r,c).jump(c,!1)}measureEndState(){const{element:l,name:u,unresolvedKeyframes:r}=this;if(!l||!l.current)return;const c=l.getValue(u);c&&c.jump(this.measuredOrigin,!1);const d=r.length-1,f=r[d];r[d]=fa[u](l.measureViewportBox(),window.getComputedStyle(l.current)),f!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=f),this.removedTransforms?.length&&this.removedTransforms.forEach(([m,y])=>{l.getValue(m).set(y)}),this.resolveNoneKeyframes()}}function aT(a,l,u){if(a instanceof EventTarget)return[a];if(typeof a=="string"){let r=document;const c=u?.[a]??r.querySelectorAll(a);return c?Array.from(c):[]}return Array.from(a)}const o0=(a,l)=>l&&typeof a=="number"?l.transform(a):a,vp=30,iT=a=>!isNaN(parseFloat(a));class lT{constructor(l,u={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,c=!0)=>{const d=ce.now();if(this.updatedAt!==d&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const f of this.dependents)f.dirty();c&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(l),this.owner=u.owner}setCurrent(l){this.current=l,this.updatedAt=ce.now(),this.canTrackVelocity===null&&l!==void 0&&(this.canTrackVelocity=iT(this.current))}setPrevFrameValue(l=this.current){this.prevFrameValue=l,this.prevUpdatedAt=this.updatedAt}onChange(l){return this.on("change",l)}on(l,u){this.events[l]||(this.events[l]=new Tc);const r=this.events[l].add(u);return l==="change"?()=>{r(),Rt.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const l in this.events)this.events[l].clear()}attach(l,u){this.passiveEffect=l,this.stopPassiveEffect=u}set(l,u=!0){!u||!this.passiveEffect?this.updateAndNotify(l,u):this.passiveEffect(l,this.updateAndNotify)}setWithVelocity(l,u,r){this.set(u),this.prev=void 0,this.prevFrameValue=l,this.prevUpdatedAt=this.updatedAt-r}jump(l,u=!0){this.updateAndNotify(l),this.prev=l,this.prevUpdatedAt=this.prevFrameValue=void 0,u&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(l){this.dependents||(this.dependents=new Set),this.dependents.add(l)}removeDependent(l){this.dependents&&this.dependents.delete(l)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const l=ce.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||l-this.updatedAt>vp)return 0;const u=Math.min(this.updatedAt-this.prevUpdatedAt,vp);return Oy(parseFloat(this.current)-parseFloat(this.prevFrameValue),u)}start(l){return this.stop(),new Promise(u=>{this.hasAnimated=!0,this.animation=l(u),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ai(a,l){return new lT(a,l)}const{schedule:Bc}=Hy(queueMicrotask,!1),Ue={x:!1,y:!1};function c0(){return Ue.x||Ue.y}function sT(a){return a==="x"||a==="y"?Ue[a]?null:(Ue[a]=!0,()=>{Ue[a]=!1}):Ue.x||Ue.y?null:(Ue.x=Ue.y=!0,()=>{Ue.x=Ue.y=!1})}function f0(a,l){const u=aT(a),r=new AbortController,c={passive:!0,...l,signal:r.signal};return[u,c,()=>r.abort()]}function bp(a){return!(a.pointerType==="touch"||c0())}function uT(a,l,u={}){const[r,c,d]=f0(a,u),f=m=>{if(!bp(m))return;const{target:y}=m,p=l(y,m);if(typeof p!="function"||!y)return;const v=b=>{bp(b)&&(p(b),y.removeEventListener("pointerleave",v))};y.addEventListener("pointerleave",v,c)};return r.forEach(m=>{m.addEventListener("pointerenter",f,c)}),d}const h0=(a,l)=>l?a===l?!0:h0(a,l.parentElement):!1,Lc=a=>a.pointerType==="mouse"?typeof a.button!="number"||a.button<=0:a.isPrimary!==!1,rT=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function oT(a){return rT.has(a.tagName)||a.tabIndex!==-1}const Ks=new WeakSet;function Sp(a){return l=>{l.key==="Enter"&&a(l)}}function Uo(a,l){a.dispatchEvent(new PointerEvent("pointer"+l,{isPrimary:!0,bubbles:!0}))}const cT=(a,l)=>{const u=a.currentTarget;if(!u)return;const r=Sp(()=>{if(Ks.has(u))return;Uo(u,"down");const c=Sp(()=>{Uo(u,"up")}),d=()=>Uo(u,"cancel");u.addEventListener("keyup",c,l),u.addEventListener("blur",d,l)});u.addEventListener("keydown",r,l),u.addEventListener("blur",()=>u.removeEventListener("keydown",r),l)};function Tp(a){return Lc(a)&&!c0()}function fT(a,l,u={}){const[r,c,d]=f0(a,u),f=m=>{const y=m.currentTarget;if(!Tp(m))return;Ks.add(y);const p=l(y,m),v=(C,j)=>{window.removeEventListener("pointerup",b),window.removeEventListener("pointercancel",T),Ks.has(y)&&Ks.delete(y),Tp(C)&&typeof p=="function"&&p(C,{success:j})},b=C=>{v(C,y===window||y===document||u.useGlobalTarget||h0(y,C.target))},T=C=>{v(C,!1)};window.addEventListener("pointerup",b,c),window.addEventListener("pointercancel",T,c)};return r.forEach(m=>{(u.useGlobalTarget?window:m).addEventListener("pointerdown",f,c),wc(m)&&(m.addEventListener("focus",p=>cT(p,c)),!oT(m)&&!m.hasAttribute("tabindex")&&(m.tabIndex=0))}),d}function d0(a){return Dy(a)&&"ownerSVGElement"in a}function hT(a){return d0(a)&&a.tagName==="svg"}const ne=a=>!!(a&&a.getVelocity),dT=[...s0,Ht,Yn],mT=a=>dT.find(l0(a)),Hc=G.createContext({transformPagePoint:a=>a,isStatic:!1,reducedMotion:"never"});class pT extends G.Component{getSnapshotBeforeUpdate(l){const u=this.props.childRef.current;if(u&&l.isPresent&&!this.props.isPresent){const r=u.offsetParent,c=wc(r)&&r.offsetWidth||0,d=this.props.sizeRef.current;d.height=u.offsetHeight||0,d.width=u.offsetWidth||0,d.top=u.offsetTop,d.left=u.offsetLeft,d.right=c-d.width-d.left}return null}componentDidUpdate(){}render(){return this.props.children}}function yT({children:a,isPresent:l,anchorX:u,root:r}){const c=G.useId(),d=G.useRef(null),f=G.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:m}=G.useContext(Hc);return G.useInsertionEffect(()=>{const{width:y,height:p,top:v,left:b,right:T}=f.current;if(l||!d.current||!y||!p)return;const C=u==="left"?`left: ${b}`:`right: ${T}`;d.current.dataset.motionPopId=c;const j=document.createElement("style");m&&(j.nonce=m);const L=r??document.head;return L.appendChild(j),j.sheet&&j.sheet.insertRule(`
          [data-motion-pop-id="${c}"] {
            position: absolute !important;
            width: ${y}px !important;
            height: ${p}px !important;
            ${C}px !important;
            top: ${v}px !important;
          }
        `),()=>{L.removeChild(j),L.contains(j)&&L.removeChild(j)}},[l]),_.jsx(pT,{isPresent:l,childRef:d,sizeRef:f,children:G.cloneElement(a,{ref:d})})}const gT=({children:a,initial:l,isPresent:u,onExitComplete:r,custom:c,presenceAffectsLayout:d,mode:f,anchorX:m,root:y})=>{const p=pc(vT),v=G.useId();let b=!0,T=G.useMemo(()=>(b=!1,{id:v,initial:l,isPresent:u,custom:c,onExitComplete:C=>{p.set(C,!0);for(const j of p.values())if(!j)return;r&&r()},register:C=>(p.set(C,!1),()=>p.delete(C))}),[u,p,r]);return d&&b&&(T={...T}),G.useMemo(()=>{p.forEach((C,j)=>p.set(j,!1))},[u]),G.useEffect(()=>{!u&&!p.size&&r&&r()},[u]),f==="popLayout"&&(a=_.jsx(yT,{isPresent:u,anchorX:m,root:y,children:a})),_.jsx(nu.Provider,{value:T,children:a})};function vT(){return new Map}function m0(a=!0){const l=G.useContext(nu);if(l===null)return[!0,null];const{isPresent:u,onExitComplete:r,register:c}=l,d=G.useId();G.useEffect(()=>{if(a)return c(d)},[a]);const f=G.useCallback(()=>a&&r&&r(d),[d,r,a]);return!u&&r?[!1,f]:[!0]}const Xs=a=>a.key||"";function xp(a){const l=[];return G.Children.forEach(a,u=>{G.isValidElement(u)&&l.push(u)}),l}const bT=({children:a,custom:l,initial:u=!0,onExitComplete:r,presenceAffectsLayout:c=!0,mode:d="sync",propagate:f=!1,anchorX:m="left",root:y})=>{const[p,v]=m0(f),b=G.useMemo(()=>xp(a),[a]),T=f&&!p?[]:b.map(Xs),C=G.useRef(!0),j=G.useRef(b),L=pc(()=>new Map),[Z,Y]=G.useState(b),[Q,q]=G.useState(b);Ey(()=>{C.current=!1,j.current=b;for(let W=0;W<Q.length;W++){const nt=Xs(Q[W]);T.includes(nt)?L.delete(nt):L.get(nt)!==!0&&L.set(nt,!1)}},[Q,T.length,T.join("-")]);const tt=[];if(b!==Z){let W=[...b];for(let nt=0;nt<Q.length;nt++){const J=Q[nt],yt=Xs(J);T.includes(yt)||(W.splice(nt,0,J),tt.push(J))}return d==="wait"&&tt.length&&(W=tt),q(xp(W)),Y(b),null}const{forceRender:H}=G.useContext(mc);return _.jsx(_.Fragment,{children:Q.map(W=>{const nt=Xs(W),J=f&&!p?!1:b===Q||T.includes(nt),yt=()=>{if(L.has(nt))L.set(nt,!0);else return;let _t=!0;L.forEach(Xt=>{Xt||(_t=!1)}),_t&&(H?.(),q(j.current),f&&v?.(),r&&r())};return _.jsx(gT,{isPresent:J,initial:!C.current||u?void 0:!1,custom:l,presenceAffectsLayout:c,mode:d,root:y,onExitComplete:J?void 0:yt,anchorX:m,children:W},nt)})})},p0=G.createContext({strict:!1}),Ap={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ii={};for(const a in Ap)ii[a]={isEnabled:l=>Ap[a].some(u=>!!l[u])};function ST(a){for(const l in a)ii[l]={...ii[l],...a[l]}}const TT=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function tu(a){return a.startsWith("while")||a.startsWith("drag")&&a!=="draggable"||a.startsWith("layout")||a.startsWith("onTap")||a.startsWith("onPan")||a.startsWith("onLayout")||TT.has(a)}let y0=a=>!tu(a);function xT(a){typeof a=="function"&&(y0=l=>l.startsWith("on")?!tu(l):a(l))}try{xT(require("@emotion/is-prop-valid").default)}catch{}function AT(a,l,u){const r={};for(const c in a)c==="values"&&typeof a.values=="object"||(y0(c)||u===!0&&tu(c)||!l&&!tu(c)||a.draggable&&c.startsWith("onDrag"))&&(r[c]=a[c]);return r}function ET(a){if(typeof Proxy>"u")return a;const l=new Map,u=(...r)=>a(...r);return new Proxy(u,{get:(r,c)=>c==="create"?a:(l.has(c)||l.set(c,a(c)),l.get(c))})}const au=G.createContext({});function iu(a){return a!==null&&typeof a=="object"&&typeof a.start=="function"}function vl(a){return typeof a=="string"||Array.isArray(a)}const qc=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Yc=["initial",...qc];function lu(a){return iu(a.animate)||Yc.some(l=>vl(a[l]))}function g0(a){return!!(lu(a)||a.variants)}function MT(a,l){if(lu(a)){const{initial:u,animate:r}=a;return{initial:u===!1||vl(u)?u:void 0,animate:vl(r)?r:void 0}}return a.inherit!==!1?l:{}}function DT(a){const{initial:l,animate:u}=MT(a,G.useContext(au));return G.useMemo(()=>({initial:l,animate:u}),[Ep(l),Ep(u)])}function Ep(a){return Array.isArray(a)?a.join(" "):a}const RT=Symbol.for("motionComponentSymbol");function Ia(a){return a&&typeof a=="object"&&Object.prototype.hasOwnProperty.call(a,"current")}function OT(a,l,u){return G.useCallback(r=>{r&&a.onMount&&a.onMount(r),l&&(r?l.mount(r):l.unmount()),u&&(typeof u=="function"?u(r):Ia(u)&&(u.current=r))},[l])}const Gc=a=>a.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),CT="framerAppearId",v0="data-"+Gc(CT),b0=G.createContext({});function _T(a,l,u,r,c){const{visualElement:d}=G.useContext(au),f=G.useContext(p0),m=G.useContext(nu),y=G.useContext(Hc).reducedMotion,p=G.useRef(null);r=r||f.renderer,!p.current&&r&&(p.current=r(a,{visualState:l,parent:d,props:u,presenceContext:m,blockInitialAnimation:m?m.initial===!1:!1,reducedMotionConfig:y}));const v=p.current,b=G.useContext(b0);v&&!v.projection&&c&&(v.type==="html"||v.type==="svg")&&NT(p.current,u,c,b);const T=G.useRef(!1);G.useInsertionEffect(()=>{v&&T.current&&v.update(u,m)});const C=u[v0],j=G.useRef(!!C&&!window.MotionHandoffIsComplete?.(C)&&window.MotionHasOptimisedAnimation?.(C));return Ey(()=>{v&&(T.current=!0,window.MotionIsMounted=!0,v.updateFeatures(),Bc.render(v.render),j.current&&v.animationState&&v.animationState.animateChanges())}),G.useEffect(()=>{v&&(!j.current&&v.animationState&&v.animationState.animateChanges(),j.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(C)}),j.current=!1))}),v}function NT(a,l,u,r){const{layoutId:c,layout:d,drag:f,dragConstraints:m,layoutScroll:y,layoutRoot:p,layoutCrossfade:v}=l;a.projection=new u(a.latestValues,l["data-framer-portal-id"]?void 0:S0(a.parent)),a.projection.setOptions({layoutId:c,layout:d,alwaysMeasureLayout:!!f||m&&Ia(m),visualElement:a,animationType:typeof d=="string"?d:"both",initialPromotionConfig:r,crossfade:v,layoutScroll:y,layoutRoot:p})}function S0(a){if(a)return a.options.allowProjection!==!1?a.projection:S0(a.parent)}function VT({preloadedFeatures:a,createVisualElement:l,useRender:u,useVisualState:r,Component:c}){a&&ST(a);function d(m,y){let p;const v={...G.useContext(Hc),...m,layoutId:zT(m)},{isStatic:b}=v,T=DT(m),C=r(m,b);if(!b&&yc){wT();const j=jT(v);p=j.MeasureLayout,T.visualElement=_T(c,C,v,l,j.ProjectionNode)}return _.jsxs(au.Provider,{value:T,children:[p&&T.visualElement?_.jsx(p,{visualElement:T.visualElement,...v}):null,u(c,m,OT(C,T.visualElement,y),C,b,T.visualElement)]})}d.displayName=`motion.${typeof c=="string"?c:`create(${c.displayName??c.name??""})`}`;const f=G.forwardRef(d);return f[RT]=c,f}function zT({layoutId:a}){const l=G.useContext(mc).id;return l&&a!==void 0?l+"-"+a:a}function wT(a,l){G.useContext(p0).strict}function jT(a){const{drag:l,layout:u}=ii;if(!l&&!u)return{};const r={...l,...u};return{MeasureLayout:l?.isEnabled(a)||u?.isEnabled(a)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const bl={};function UT(a){for(const l in a)bl[l]=a[l],Ec(l)&&(bl[l].isCSSVariable=!0)}function T0(a,{layout:l,layoutId:u}){return ui.has(a)||a.startsWith("origin")||(l||u!==void 0)&&(!!bl[a]||a==="opacity")}const BT={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},LT=si.length;function HT(a,l,u){let r="",c=!0;for(let d=0;d<LT;d++){const f=si[d],m=a[f];if(m===void 0)continue;let y=!0;if(typeof m=="number"?y=m===(f.startsWith("scale")?1:0):y=parseFloat(m)===0,!y||u){const p=o0(m,Uc[f]);if(!y){c=!1;const v=BT[f]||f;r+=`${v}(${p}) `}u&&(l[f]=p)}}return r=r.trim(),u?r=u(l,c?"":r):c&&(r="none"),r}function Xc(a,l,u){const{style:r,vars:c,transformOrigin:d}=a;let f=!1,m=!1;for(const y in l){const p=l[y];if(ui.has(y)){f=!0;continue}else if(Ec(y)){c[y]=p;continue}else{const v=o0(p,Uc[y]);y.startsWith("origin")?(m=!0,d[y]=v):r[y]=v}}if(l.transform||(f||u?r.transform=HT(l,a.transform,u):r.transform&&(r.transform="none")),m){const{originX:y="50%",originY:p="50%",originZ:v=0}=d;r.transformOrigin=`${y} ${p} ${v}`}}const Zc=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function x0(a,l,u){for(const r in l)!ne(l[r])&&!T0(r,u)&&(a[r]=l[r])}function qT({transformTemplate:a},l){return G.useMemo(()=>{const u=Zc();return Xc(u,l,a),Object.assign({},u.vars,u.style)},[l])}function YT(a,l){const u=a.style||{},r={};return x0(r,u,a),Object.assign(r,qT(a,l)),r}function GT(a,l){const u={},r=YT(a,l);return a.drag&&a.dragListener!==!1&&(u.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=a.drag===!0?"none":`pan-${a.drag==="x"?"y":"x"}`),a.tabIndex===void 0&&(a.onTap||a.onTapStart||a.whileTap)&&(u.tabIndex=0),u.style=r,u}const XT={offset:"stroke-dashoffset",array:"stroke-dasharray"},ZT={offset:"strokeDashoffset",array:"strokeDasharray"};function QT(a,l,u=1,r=0,c=!0){a.pathLength=1;const d=c?XT:ZT;a[d.offset]=it.transform(-r);const f=it.transform(l),m=it.transform(u);a[d.array]=`${f} ${m}`}function A0(a,{attrX:l,attrY:u,attrScale:r,pathLength:c,pathSpacing:d=1,pathOffset:f=0,...m},y,p,v){if(Xc(a,m,p),y){a.style.viewBox&&(a.attrs.viewBox=a.style.viewBox);return}a.attrs=a.style,a.style={};const{attrs:b,style:T}=a;b.transform&&(T.transform=b.transform,delete b.transform),(T.transform||b.transformOrigin)&&(T.transformOrigin=b.transformOrigin??"50% 50%",delete b.transformOrigin),T.transform&&(T.transformBox=v?.transformBox??"fill-box",delete b.transformBox),l!==void 0&&(b.x=l),u!==void 0&&(b.y=u),r!==void 0&&(b.scale=r),c!==void 0&&QT(b,c,d,f,!1)}const E0=()=>({...Zc(),attrs:{}}),M0=a=>typeof a=="string"&&a.toLowerCase()==="svg";function KT(a,l,u,r){const c=G.useMemo(()=>{const d=E0();return A0(d,l,M0(r),a.transformTemplate,a.style),{...d.attrs,style:{...d.style}}},[l]);if(a.style){const d={};x0(d,a.style,a),c.style={...d,...c.style}}return c}const kT=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Qc(a){return typeof a!="string"||a.includes("-")?!1:!!(kT.indexOf(a)>-1||/[A-Z]/u.test(a))}function PT(a=!1){return(u,r,c,{latestValues:d},f)=>{const y=(Qc(u)?KT:GT)(r,d,f,u),p=AT(r,typeof u=="string",a),v=u!==G.Fragment?{...p,...y,ref:c}:{},{children:b}=r,T=G.useMemo(()=>ne(b)?b.get():b,[b]);return G.createElement(u,{...v,children:T})}}function Mp(a){const l=[{},{}];return a?.values.forEach((u,r)=>{l[0][r]=u.get(),l[1][r]=u.getVelocity()}),l}function Kc(a,l,u,r){if(typeof l=="function"){const[c,d]=Mp(r);l=l(u!==void 0?u:a.custom,c,d)}if(typeof l=="string"&&(l=a.variants&&a.variants[l]),typeof l=="function"){const[c,d]=Mp(r);l=l(u!==void 0?u:a.custom,c,d)}return l}function ks(a){return ne(a)?a.get():a}function JT({scrapeMotionValuesFromProps:a,createRenderState:l},u,r,c){return{latestValues:FT(u,r,c,a),renderState:l()}}const D0=a=>(l,u)=>{const r=G.useContext(au),c=G.useContext(nu),d=()=>JT(a,l,r,c);return u?d():pc(d)};function FT(a,l,u,r){const c={},d=r(a,{});for(const T in d)c[T]=ks(d[T]);let{initial:f,animate:m}=a;const y=lu(a),p=g0(a);l&&p&&!y&&a.inherit!==!1&&(f===void 0&&(f=l.initial),m===void 0&&(m=l.animate));let v=u?u.initial===!1:!1;v=v||f===!1;const b=v?m:f;if(b&&typeof b!="boolean"&&!iu(b)){const T=Array.isArray(b)?b:[b];for(let C=0;C<T.length;C++){const j=Kc(a,T[C]);if(j){const{transitionEnd:L,transition:Z,...Y}=j;for(const Q in Y){let q=Y[Q];if(Array.isArray(q)){const tt=v?q.length-1:0;q=q[tt]}q!==null&&(c[Q]=q)}for(const Q in L)c[Q]=L[Q]}}}return c}function kc(a,l,u){const{style:r}=a,c={};for(const d in r)(ne(r[d])||l.style&&ne(l.style[d])||T0(d,a)||u?.getValue(d)?.liveStyle!==void 0)&&(c[d]=r[d]);return c}const WT={useVisualState:D0({scrapeMotionValuesFromProps:kc,createRenderState:Zc})};function R0(a,l,u){const r=kc(a,l,u);for(const c in a)if(ne(a[c])||ne(l[c])){const d=si.indexOf(c)!==-1?"attr"+c.charAt(0).toUpperCase()+c.substring(1):c;r[d]=a[c]}return r}const $T={useVisualState:D0({scrapeMotionValuesFromProps:R0,createRenderState:E0})};function IT(a,l){return function(r,{forwardMotionProps:c}={forwardMotionProps:!1}){const f={...Qc(r)?$T:WT,preloadedFeatures:a,useRender:PT(c),createVisualElement:l,Component:r};return VT(f)}}function Sl(a,l,u){const r=a.getProps();return Kc(r,l,u!==void 0?u:r.custom,a)}const ic=a=>Array.isArray(a);function tx(a,l,u){a.hasValue(l)?a.getValue(l).set(u):a.addValue(l,ai(u))}function ex(a){return ic(a)?a[a.length-1]||0:a}function nx(a,l){const u=Sl(a,l);let{transitionEnd:r={},transition:c={},...d}=u||{};d={...d,...r};for(const f in d){const m=ex(d[f]);tx(a,f,m)}}function ax(a){return!!(ne(a)&&a.add)}function lc(a,l){const u=a.getValue("willChange");if(ax(u))return u.add(l);if(!u&&dn.WillChange){const r=new dn.WillChange("auto");a.addValue("willChange",r),r.add(l)}}function O0(a){return a.props[v0]}const ix=a=>a!==null;function lx(a,{repeat:l,repeatType:u="loop"},r){const c=a.filter(ix),d=l&&u!=="loop"&&l%2===1?0:c.length-1;return c[d]}const sx={type:"spring",stiffness:500,damping:25,restSpeed:10},ux=a=>({type:"spring",stiffness:550,damping:a===0?2*Math.sqrt(550):30,restSpeed:10}),rx={type:"keyframes",duration:.8},ox={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},cx=(a,{keyframes:l})=>l.length>2?rx:ui.has(a)?a.startsWith("scale")?ux(l[1]):sx:ox;function fx({when:a,delay:l,delayChildren:u,staggerChildren:r,staggerDirection:c,repeat:d,repeatType:f,repeatDelay:m,from:y,elapsed:p,...v}){return!!Object.keys(v).length}const Pc=(a,l,u,r={},c,d)=>f=>{const m=jc(r,a)||{},y=m.delay||r.delay||0;let{elapsed:p=0}=r;p=p-Ke(y);const v={keyframes:Array.isArray(u)?u:[null,u],ease:"easeOut",velocity:l.getVelocity(),...m,delay:-p,onUpdate:T=>{l.set(T),m.onUpdate&&m.onUpdate(T)},onComplete:()=>{f(),m.onComplete&&m.onComplete()},name:a,motionValue:l,element:d?void 0:c};fx(m)||Object.assign(v,cx(a,v)),v.duration&&(v.duration=Ke(v.duration)),v.repeatDelay&&(v.repeatDelay=Ke(v.repeatDelay)),v.from!==void 0&&(v.keyframes[0]=v.from);let b=!1;if((v.type===!1||v.duration===0&&!v.repeatDelay)&&(v.duration=0,v.delay===0&&(b=!0)),(dn.instantAnimations||dn.skipAnimations)&&(b=!0,v.duration=0,v.delay=0),v.allowFlatten=!m.type&&!m.ease,b&&!d&&l.get()!==void 0){const T=lx(v.keyframes,m);if(T!==void 0){Rt.update(()=>{v.onUpdate(T),v.onComplete()});return}}return m.isSync?new Vc(v):new ZS(v)};function hx({protectedKeys:a,needsAnimating:l},u){const r=a.hasOwnProperty(u)&&l[u]!==!0;return l[u]=!1,r}function C0(a,l,{delay:u=0,transitionOverride:r,type:c}={}){let{transition:d=a.getDefaultTransition(),transitionEnd:f,...m}=l;r&&(d=r);const y=[],p=c&&a.animationState&&a.animationState.getState()[c];for(const v in m){const b=a.getValue(v,a.latestValues[v]??null),T=m[v];if(T===void 0||p&&hx(p,v))continue;const C={delay:u,...jc(d||{},v)},j=b.get();if(j!==void 0&&!b.isAnimating&&!Array.isArray(T)&&T===j&&!C.velocity)continue;let L=!1;if(window.MotionHandoffAnimation){const Y=O0(a);if(Y){const Q=window.MotionHandoffAnimation(Y,v,Rt);Q!==null&&(C.startTime=Q,L=!0)}}lc(a,v),b.start(Pc(v,b,T,a.shouldReduceMotion&&i0.has(v)?{type:!1}:C,a,L));const Z=b.animation;Z&&y.push(Z)}return f&&Promise.all(y).then(()=>{Rt.update(()=>{f&&nx(a,f)})}),y}function sc(a,l,u={}){const r=Sl(a,l,u.type==="exit"?a.presenceContext?.custom:void 0);let{transition:c=a.getDefaultTransition()||{}}=r||{};u.transitionOverride&&(c=u.transitionOverride);const d=r?()=>Promise.all(C0(a,r,u)):()=>Promise.resolve(),f=a.variantChildren&&a.variantChildren.size?(y=0)=>{const{delayChildren:p=0,staggerChildren:v,staggerDirection:b}=c;return dx(a,l,y,p,v,b,u)}:()=>Promise.resolve(),{when:m}=c;if(m){const[y,p]=m==="beforeChildren"?[d,f]:[f,d];return y().then(()=>p())}else return Promise.all([d(),f(u.delay)])}function dx(a,l,u=0,r=0,c=0,d=1,f){const m=[],y=a.variantChildren.size,p=(y-1)*c,v=typeof r=="function",b=v?T=>r(T,y):d===1?(T=0)=>T*c:(T=0)=>p-T*c;return Array.from(a.variantChildren).sort(mx).forEach((T,C)=>{T.notify("AnimationStart",l),m.push(sc(T,l,{...f,delay:u+(v?0:r)+b(C)}).then(()=>T.notify("AnimationComplete",l)))}),Promise.all(m)}function mx(a,l){return a.sortNodePosition(l)}function px(a,l,u={}){a.notify("AnimationStart",l);let r;if(Array.isArray(l)){const c=l.map(d=>sc(a,d,u));r=Promise.all(c)}else if(typeof l=="string")r=sc(a,l,u);else{const c=typeof l=="function"?Sl(a,l,u.custom):l;r=Promise.all(C0(a,c,u))}return r.then(()=>{a.notify("AnimationComplete",l)})}function _0(a,l){if(!Array.isArray(l))return!1;const u=l.length;if(u!==a.length)return!1;for(let r=0;r<u;r++)if(l[r]!==a[r])return!1;return!0}const yx=Yc.length;function N0(a){if(!a)return;if(!a.isControllingVariants){const u=a.parent?N0(a.parent)||{}:{};return a.props.initial!==void 0&&(u.initial=a.props.initial),u}const l={};for(let u=0;u<yx;u++){const r=Yc[u],c=a.props[r];(vl(c)||c===!1)&&(l[r]=c)}return l}const gx=[...qc].reverse(),vx=qc.length;function bx(a){return l=>Promise.all(l.map(({animation:u,options:r})=>px(a,u,r)))}function Sx(a){let l=bx(a),u=Dp(),r=!0;const c=y=>(p,v)=>{const b=Sl(a,v,y==="exit"?a.presenceContext?.custom:void 0);if(b){const{transition:T,transitionEnd:C,...j}=b;p={...p,...j,...C}}return p};function d(y){l=y(a)}function f(y){const{props:p}=a,v=N0(a.parent)||{},b=[],T=new Set;let C={},j=1/0;for(let Z=0;Z<vx;Z++){const Y=gx[Z],Q=u[Y],q=p[Y]!==void 0?p[Y]:v[Y],tt=vl(q),H=Y===y?Q.isActive:null;H===!1&&(j=Z);let W=q===v[Y]&&q!==p[Y]&&tt;if(W&&r&&a.manuallyAnimateOnMount&&(W=!1),Q.protectedKeys={...C},!Q.isActive&&H===null||!q&&!Q.prevProp||iu(q)||typeof q=="boolean")continue;const nt=Tx(Q.prevProp,q);let J=nt||Y===y&&Q.isActive&&!W&&tt||Z>j&&tt,yt=!1;const _t=Array.isArray(q)?q:[q];let Xt=_t.reduce(c(Y),{});H===!1&&(Xt={});const{prevResolvedValues:qt={}}=Q,Je={...qt,...Xt},He=B=>{J=!0,T.has(B)&&(yt=!0,T.delete(B)),Q.needsAnimating[B]=!0;const P=a.getValue(B);P&&(P.liveStyle=!1)};for(const B in Je){const P=Xt[B],mt=qt[B];if(C.hasOwnProperty(B))continue;let x=!1;ic(P)&&ic(mt)?x=!_0(P,mt):x=P!==mt,x?P!=null?He(B):T.add(B):P!==void 0&&T.has(B)?He(B):Q.protectedKeys[B]=!0}Q.prevProp=q,Q.prevResolvedValues=Xt,Q.isActive&&(C={...C,...Xt}),r&&a.blockInitialAnimation&&(J=!1),J&&(!(W&&nt)||yt)&&b.push(..._t.map(B=>({animation:B,options:{type:Y}})))}if(T.size){const Z={};if(typeof p.initial!="boolean"){const Y=Sl(a,Array.isArray(p.initial)?p.initial[0]:p.initial);Y&&Y.transition&&(Z.transition=Y.transition)}T.forEach(Y=>{const Q=a.getBaseTarget(Y),q=a.getValue(Y);q&&(q.liveStyle=!0),Z[Y]=Q??null}),b.push({animation:Z})}let L=!!b.length;return r&&(p.initial===!1||p.initial===p.animate)&&!a.manuallyAnimateOnMount&&(L=!1),r=!1,L?l(b):Promise.resolve()}function m(y,p){if(u[y].isActive===p)return Promise.resolve();a.variantChildren?.forEach(b=>b.animationState?.setActive(y,p)),u[y].isActive=p;const v=f(y);for(const b in u)u[b].protectedKeys={};return v}return{animateChanges:f,setActive:m,setAnimateFunction:d,getState:()=>u,reset:()=>{u=Dp(),r=!0}}}function Tx(a,l){return typeof l=="string"?l!==a:Array.isArray(l)?!_0(l,a):!1}function ua(a=!1){return{isActive:a,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Dp(){return{animate:ua(!0),whileInView:ua(),whileHover:ua(),whileTap:ua(),whileDrag:ua(),whileFocus:ua(),exit:ua()}}class Gn{constructor(l){this.isMounted=!1,this.node=l}update(){}}class xx extends Gn{constructor(l){super(l),l.animationState||(l.animationState=Sx(l))}updateAnimationControlsSubscription(){const{animate:l}=this.node.getProps();iu(l)&&(this.unmountControls=l.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:l}=this.node.getProps(),{animate:u}=this.node.prevProps||{};l!==u&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let Ax=0;class Ex extends Gn{constructor(){super(...arguments),this.id=Ax++}update(){if(!this.node.presenceContext)return;const{isPresent:l,onExitComplete:u}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||l===r)return;const c=this.node.animationState.setActive("exit",!l);u&&!l&&c.then(()=>{u(this.id)})}mount(){const{register:l,onExitComplete:u}=this.node.presenceContext||{};u&&u(this.id),l&&(this.unmount=l(this.id))}unmount(){}}const Mx={animation:{Feature:xx},exit:{Feature:Ex}};function Tl(a,l,u,r={passive:!0}){return a.addEventListener(l,u,r),()=>a.removeEventListener(l,u)}function Ml(a){return{point:{x:a.pageX,y:a.pageY}}}const Dx=a=>l=>Lc(l)&&a(l,Ml(l));function hl(a,l,u,r){return Tl(a,l,Dx(u),r)}function V0({top:a,left:l,right:u,bottom:r}){return{x:{min:l,max:u},y:{min:a,max:r}}}function Rx({x:a,y:l}){return{top:l.min,right:a.max,bottom:l.max,left:a.min}}function Ox(a,l){if(!l)return a;const u=l({x:a.left,y:a.top}),r=l({x:a.right,y:a.bottom});return{top:u.y,left:u.x,bottom:r.y,right:r.x}}const z0=1e-4,Cx=1-z0,_x=1+z0,w0=.01,Nx=0-w0,Vx=0+w0;function ie(a){return a.max-a.min}function zx(a,l,u){return Math.abs(a-l)<=u}function Rp(a,l,u,r=.5){a.origin=r,a.originPoint=Ct(l.min,l.max,a.origin),a.scale=ie(u)/ie(l),a.translate=Ct(u.min,u.max,a.origin)-a.originPoint,(a.scale>=Cx&&a.scale<=_x||isNaN(a.scale))&&(a.scale=1),(a.translate>=Nx&&a.translate<=Vx||isNaN(a.translate))&&(a.translate=0)}function dl(a,l,u,r){Rp(a.x,l.x,u.x,r?r.originX:void 0),Rp(a.y,l.y,u.y,r?r.originY:void 0)}function Op(a,l,u){a.min=u.min+l.min,a.max=a.min+ie(l)}function wx(a,l,u){Op(a.x,l.x,u.x),Op(a.y,l.y,u.y)}function Cp(a,l,u){a.min=l.min-u.min,a.max=a.min+ie(l)}function ml(a,l,u){Cp(a.x,l.x,u.x),Cp(a.y,l.y,u.y)}const _p=()=>({translate:0,scale:1,origin:0,originPoint:0}),ti=()=>({x:_p(),y:_p()}),Np=()=>({min:0,max:0}),Ut=()=>({x:Np(),y:Np()});function Ne(a){return[a("x"),a("y")]}function Bo(a){return a===void 0||a===1}function uc({scale:a,scaleX:l,scaleY:u}){return!Bo(a)||!Bo(l)||!Bo(u)}function ra(a){return uc(a)||j0(a)||a.z||a.rotate||a.rotateX||a.rotateY||a.skewX||a.skewY}function j0(a){return Vp(a.x)||Vp(a.y)}function Vp(a){return a&&a!=="0%"}function eu(a,l,u){const r=a-u,c=l*r;return u+c}function zp(a,l,u,r,c){return c!==void 0&&(a=eu(a,c,r)),eu(a,u,r)+l}function rc(a,l=0,u=1,r,c){a.min=zp(a.min,l,u,r,c),a.max=zp(a.max,l,u,r,c)}function U0(a,{x:l,y:u}){rc(a.x,l.translate,l.scale,l.originPoint),rc(a.y,u.translate,u.scale,u.originPoint)}const wp=.999999999999,jp=1.0000000000001;function jx(a,l,u,r=!1){const c=u.length;if(!c)return;l.x=l.y=1;let d,f;for(let m=0;m<c;m++){d=u[m],f=d.projectionDelta;const{visualElement:y}=d.options;y&&y.props.style&&y.props.style.display==="contents"||(r&&d.options.layoutScroll&&d.scroll&&d!==d.root&&ni(a,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),f&&(l.x*=f.x.scale,l.y*=f.y.scale,U0(a,f)),r&&ra(d.latestValues)&&ni(a,d.latestValues))}l.x<jp&&l.x>wp&&(l.x=1),l.y<jp&&l.y>wp&&(l.y=1)}function ei(a,l){a.min=a.min+l,a.max=a.max+l}function Up(a,l,u,r,c=.5){const d=Ct(a.min,a.max,c);rc(a,l,u,d,r)}function ni(a,l){Up(a.x,l.x,l.scaleX,l.scale,l.originX),Up(a.y,l.y,l.scaleY,l.scale,l.originY)}function B0(a,l){return V0(Ox(a.getBoundingClientRect(),l))}function Ux(a,l,u){const r=B0(a,u),{scroll:c}=l;return c&&(ei(r.x,c.offset.x),ei(r.y,c.offset.y)),r}const L0=({current:a})=>a?a.ownerDocument.defaultView:null,Bp=(a,l)=>Math.abs(a-l);function Bx(a,l){const u=Bp(a.x,l.x),r=Bp(a.y,l.y);return Math.sqrt(u**2+r**2)}class H0{constructor(l,u,{transformPagePoint:r,contextWindow:c=window,dragSnapToOrigin:d=!1,distanceThreshold:f=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const T=Ho(this.lastMoveEventInfo,this.history),C=this.startEvent!==null,j=Bx(T.offset,{x:0,y:0})>=this.distanceThreshold;if(!C&&!j)return;const{point:L}=T,{timestamp:Z}=$t;this.history.push({...L,timestamp:Z});const{onStart:Y,onMove:Q}=this.handlers;C||(Y&&Y(this.lastMoveEvent,T),this.startEvent=this.lastMoveEvent),Q&&Q(this.lastMoveEvent,T)},this.handlePointerMove=(T,C)=>{this.lastMoveEvent=T,this.lastMoveEventInfo=Lo(C,this.transformPagePoint),Rt.update(this.updatePoint,!0)},this.handlePointerUp=(T,C)=>{this.end();const{onEnd:j,onSessionEnd:L,resumeAnimation:Z}=this.handlers;if(this.dragSnapToOrigin&&Z&&Z(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const Y=Ho(T.type==="pointercancel"?this.lastMoveEventInfo:Lo(C,this.transformPagePoint),this.history);this.startEvent&&j&&j(T,Y),L&&L(T,Y)},!Lc(l))return;this.dragSnapToOrigin=d,this.handlers=u,this.transformPagePoint=r,this.distanceThreshold=f,this.contextWindow=c||window;const m=Ml(l),y=Lo(m,this.transformPagePoint),{point:p}=y,{timestamp:v}=$t;this.history=[{...p,timestamp:v}];const{onSessionStart:b}=u;b&&b(l,Ho(y,this.history)),this.removeListeners=xl(hl(this.contextWindow,"pointermove",this.handlePointerMove),hl(this.contextWindow,"pointerup",this.handlePointerUp),hl(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(l){this.handlers=l}end(){this.removeListeners&&this.removeListeners(),qn(this.updatePoint)}}function Lo(a,l){return l?{point:l(a.point)}:a}function Lp(a,l){return{x:a.x-l.x,y:a.y-l.y}}function Ho({point:a},l){return{point:a,delta:Lp(a,q0(l)),offset:Lp(a,Lx(l)),velocity:Hx(l,.1)}}function Lx(a){return a[0]}function q0(a){return a[a.length-1]}function Hx(a,l){if(a.length<2)return{x:0,y:0};let u=a.length-1,r=null;const c=q0(a);for(;u>=0&&(r=a[u],!(c.timestamp-r.timestamp>Ke(l)));)u--;if(!r)return{x:0,y:0};const d=ke(c.timestamp-r.timestamp);if(d===0)return{x:0,y:0};const f={x:(c.x-r.x)/d,y:(c.y-r.y)/d};return f.x===1/0&&(f.x=0),f.y===1/0&&(f.y=0),f}function qx(a,{min:l,max:u},r){return l!==void 0&&a<l?a=r?Ct(l,a,r.min):Math.max(a,l):u!==void 0&&a>u&&(a=r?Ct(u,a,r.max):Math.min(a,u)),a}function Hp(a,l,u){return{min:l!==void 0?a.min+l:void 0,max:u!==void 0?a.max+u-(a.max-a.min):void 0}}function Yx(a,{top:l,left:u,bottom:r,right:c}){return{x:Hp(a.x,u,c),y:Hp(a.y,l,r)}}function qp(a,l){let u=l.min-a.min,r=l.max-a.max;return l.max-l.min<a.max-a.min&&([u,r]=[r,u]),{min:u,max:r}}function Gx(a,l){return{x:qp(a.x,l.x),y:qp(a.y,l.y)}}function Xx(a,l){let u=.5;const r=ie(a),c=ie(l);return c>r?u=pl(l.min,l.max-r,a.min):r>c&&(u=pl(a.min,a.max-c,l.min)),hn(0,1,u)}function Zx(a,l){const u={};return l.min!==void 0&&(u.min=l.min-a.min),l.max!==void 0&&(u.max=l.max-a.min),u}const oc=.35;function Qx(a=oc){return a===!1?a=0:a===!0&&(a=oc),{x:Yp(a,"left","right"),y:Yp(a,"top","bottom")}}function Yp(a,l,u){return{min:Gp(a,l),max:Gp(a,u)}}function Gp(a,l){return typeof a=="number"?a:a[l]||0}const Kx=new WeakMap;class kx{constructor(l){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=Ut(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=l}start(l,{snapToCursor:u=!1,distanceThreshold:r}={}){const{presenceContext:c}=this.visualElement;if(c&&c.isPresent===!1)return;const d=b=>{const{dragSnapToOrigin:T}=this.getProps();T?this.pauseAnimation():this.stopAnimation(),u&&this.snapToCursor(Ml(b).point)},f=(b,T)=>{const{drag:C,dragPropagation:j,onDragStart:L}=this.getProps();if(C&&!j&&(this.openDragLock&&this.openDragLock(),this.openDragLock=sT(C),!this.openDragLock))return;this.latestPointerEvent=b,this.latestPanInfo=T,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ne(Y=>{let Q=this.getAxisMotionValue(Y).get()||0;if(Pe.test(Q)){const{projection:q}=this.visualElement;if(q&&q.layout){const tt=q.layout.layoutBox[Y];tt&&(Q=ie(tt)*(parseFloat(Q)/100))}}this.originPoint[Y]=Q}),L&&Rt.postRender(()=>L(b,T)),lc(this.visualElement,"transform");const{animationState:Z}=this.visualElement;Z&&Z.setActive("whileDrag",!0)},m=(b,T)=>{this.latestPointerEvent=b,this.latestPanInfo=T;const{dragPropagation:C,dragDirectionLock:j,onDirectionLock:L,onDrag:Z}=this.getProps();if(!C&&!this.openDragLock)return;const{offset:Y}=T;if(j&&this.currentDirection===null){this.currentDirection=Px(Y),this.currentDirection!==null&&L&&L(this.currentDirection);return}this.updateAxis("x",T.point,Y),this.updateAxis("y",T.point,Y),this.visualElement.render(),Z&&Z(b,T)},y=(b,T)=>{this.latestPointerEvent=b,this.latestPanInfo=T,this.stop(b,T),this.latestPointerEvent=null,this.latestPanInfo=null},p=()=>Ne(b=>this.getAnimationState(b)==="paused"&&this.getAxisMotionValue(b).animation?.play()),{dragSnapToOrigin:v}=this.getProps();this.panSession=new H0(l,{onSessionStart:d,onStart:f,onMove:m,onSessionEnd:y,resumeAnimation:p},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:v,distanceThreshold:r,contextWindow:L0(this.visualElement)})}stop(l,u){const r=l||this.latestPointerEvent,c=u||this.latestPanInfo,d=this.isDragging;if(this.cancel(),!d||!c||!r)return;const{velocity:f}=c;this.startAnimation(f);const{onDragEnd:m}=this.getProps();m&&Rt.postRender(()=>m(r,c))}cancel(){this.isDragging=!1;const{projection:l,animationState:u}=this.visualElement;l&&(l.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),u&&u.setActive("whileDrag",!1)}updateAxis(l,u,r){const{drag:c}=this.getProps();if(!r||!Zs(l,c,this.currentDirection))return;const d=this.getAxisMotionValue(l);let f=this.originPoint[l]+r[l];this.constraints&&this.constraints[l]&&(f=qx(f,this.constraints[l],this.elastic[l])),d.set(f)}resolveConstraints(){const{dragConstraints:l,dragElastic:u}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,c=this.constraints;l&&Ia(l)?this.constraints||(this.constraints=this.resolveRefConstraints()):l&&r?this.constraints=Yx(r.layoutBox,l):this.constraints=!1,this.elastic=Qx(u),c!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&Ne(d=>{this.constraints!==!1&&this.getAxisMotionValue(d)&&(this.constraints[d]=Zx(r.layoutBox[d],this.constraints[d]))})}resolveRefConstraints(){const{dragConstraints:l,onMeasureDragConstraints:u}=this.getProps();if(!l||!Ia(l))return!1;const r=l.current,{projection:c}=this.visualElement;if(!c||!c.layout)return!1;const d=Ux(r,c.root,this.visualElement.getTransformPagePoint());let f=Gx(c.layout.layoutBox,d);if(u){const m=u(Rx(f));this.hasMutatedConstraints=!!m,m&&(f=V0(m))}return f}startAnimation(l){const{drag:u,dragMomentum:r,dragElastic:c,dragTransition:d,dragSnapToOrigin:f,onDragTransitionEnd:m}=this.getProps(),y=this.constraints||{},p=Ne(v=>{if(!Zs(v,u,this.currentDirection))return;let b=y&&y[v]||{};f&&(b={min:0,max:0});const T=c?200:1e6,C=c?40:1e7,j={type:"inertia",velocity:r?l[v]:0,bounceStiffness:T,bounceDamping:C,timeConstant:750,restDelta:1,restSpeed:10,...d,...b};return this.startAxisValueAnimation(v,j)});return Promise.all(p).then(m)}startAxisValueAnimation(l,u){const r=this.getAxisMotionValue(l);return lc(this.visualElement,l),r.start(Pc(l,r,0,u,this.visualElement,!1))}stopAnimation(){Ne(l=>this.getAxisMotionValue(l).stop())}pauseAnimation(){Ne(l=>this.getAxisMotionValue(l).animation?.pause())}getAnimationState(l){return this.getAxisMotionValue(l).animation?.state}getAxisMotionValue(l){const u=`_drag${l.toUpperCase()}`,r=this.visualElement.getProps(),c=r[u];return c||this.visualElement.getValue(l,(r.initial?r.initial[l]:void 0)||0)}snapToCursor(l){Ne(u=>{const{drag:r}=this.getProps();if(!Zs(u,r,this.currentDirection))return;const{projection:c}=this.visualElement,d=this.getAxisMotionValue(u);if(c&&c.layout){const{min:f,max:m}=c.layout.layoutBox[u];d.set(l[u]-Ct(f,m,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:l,dragConstraints:u}=this.getProps(),{projection:r}=this.visualElement;if(!Ia(u)||!r||!this.constraints)return;this.stopAnimation();const c={x:0,y:0};Ne(f=>{const m=this.getAxisMotionValue(f);if(m&&this.constraints!==!1){const y=m.get();c[f]=Xx({min:y,max:y},this.constraints[f])}});const{transformTemplate:d}=this.visualElement.getProps();this.visualElement.current.style.transform=d?d({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Ne(f=>{if(!Zs(f,l,null))return;const m=this.getAxisMotionValue(f),{min:y,max:p}=this.constraints[f];m.set(Ct(y,p,c[f]))})}addListeners(){if(!this.visualElement.current)return;Kx.set(this.visualElement,this);const l=this.visualElement.current,u=hl(l,"pointerdown",y=>{const{drag:p,dragListener:v=!0}=this.getProps();p&&v&&this.start(y)}),r=()=>{const{dragConstraints:y}=this.getProps();Ia(y)&&y.current&&(this.constraints=this.resolveRefConstraints())},{projection:c}=this.visualElement,d=c.addEventListener("measure",r);c&&!c.layout&&(c.root&&c.root.updateScroll(),c.updateLayout()),Rt.read(r);const f=Tl(window,"resize",()=>this.scalePositionWithinConstraints()),m=c.addEventListener("didUpdate",({delta:y,hasLayoutChanged:p})=>{this.isDragging&&p&&(Ne(v=>{const b=this.getAxisMotionValue(v);b&&(this.originPoint[v]+=y[v].translate,b.set(b.get()+y[v].translate))}),this.visualElement.render())});return()=>{f(),u(),d(),m&&m()}}getProps(){const l=this.visualElement.getProps(),{drag:u=!1,dragDirectionLock:r=!1,dragPropagation:c=!1,dragConstraints:d=!1,dragElastic:f=oc,dragMomentum:m=!0}=l;return{...l,drag:u,dragDirectionLock:r,dragPropagation:c,dragConstraints:d,dragElastic:f,dragMomentum:m}}}function Zs(a,l,u){return(l===!0||l===a)&&(u===null||u===a)}function Px(a,l=10){let u=null;return Math.abs(a.y)>l?u="y":Math.abs(a.x)>l&&(u="x"),u}class Jx extends Gn{constructor(l){super(l),this.removeGroupControls=Ve,this.removeListeners=Ve,this.controls=new kx(l)}mount(){const{dragControls:l}=this.node.getProps();l&&(this.removeGroupControls=l.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Ve}unmount(){this.removeGroupControls(),this.removeListeners()}}const Xp=a=>(l,u)=>{a&&Rt.postRender(()=>a(l,u))};class Fx extends Gn{constructor(){super(...arguments),this.removePointerDownListener=Ve}onPointerDown(l){this.session=new H0(l,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:L0(this.node)})}createPanHandlers(){const{onPanSessionStart:l,onPanStart:u,onPan:r,onPanEnd:c}=this.node.getProps();return{onSessionStart:Xp(l),onStart:Xp(u),onMove:r,onEnd:(d,f)=>{delete this.session,c&&Rt.postRender(()=>c(d,f))}}}mount(){this.removePointerDownListener=hl(this.node.current,"pointerdown",l=>this.onPointerDown(l))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Ps={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Zp(a,l){return l.max===l.min?0:a/(l.max-l.min)*100}const rl={correct:(a,l)=>{if(!l.target)return a;if(typeof a=="string")if(it.test(a))a=parseFloat(a);else return a;const u=Zp(a,l.target.x),r=Zp(a,l.target.y);return`${u}% ${r}%`}},Wx={correct:(a,{treeScale:l,projectionDelta:u})=>{const r=a,c=Yn.parse(a);if(c.length>5)return r;const d=Yn.createTransformer(a),f=typeof c[0]!="number"?1:0,m=u.x.scale*l.x,y=u.y.scale*l.y;c[0+f]/=m,c[1+f]/=y;const p=Ct(m,y,.5);return typeof c[2+f]=="number"&&(c[2+f]/=p),typeof c[3+f]=="number"&&(c[3+f]/=p),d(c)}};let Qp=!1;class $x extends G.Component{componentDidMount(){const{visualElement:l,layoutGroup:u,switchLayoutGroup:r,layoutId:c}=this.props,{projection:d}=l;UT(Ix),d&&(u.group&&u.group.add(d),r&&r.register&&c&&r.register(d),Qp&&d.root.didUpdate(),d.addEventListener("animationComplete",()=>{this.safeToRemove()}),d.setOptions({...d.options,onExitComplete:()=>this.safeToRemove()})),Ps.hasEverUpdated=!0}getSnapshotBeforeUpdate(l){const{layoutDependency:u,visualElement:r,drag:c,isPresent:d}=this.props,{projection:f}=r;return f&&(f.isPresent=d,Qp=!0,c||l.layoutDependency!==u||u===void 0||l.isPresent!==d?f.willUpdate():this.safeToRemove(),l.isPresent!==d&&(d?f.promote():f.relegate()||Rt.postRender(()=>{const m=f.getStack();(!m||!m.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:l}=this.props.visualElement;l&&(l.root.didUpdate(),Bc.postRender(()=>{!l.currentAnimation&&l.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:l,layoutGroup:u,switchLayoutGroup:r}=this.props,{projection:c}=l;c&&(c.scheduleCheckAfterUnmount(),u&&u.group&&u.group.remove(c),r&&r.deregister&&r.deregister(c))}safeToRemove(){const{safeToRemove:l}=this.props;l&&l()}render(){return null}}function Y0(a){const[l,u]=m0(),r=G.useContext(mc);return _.jsx($x,{...a,layoutGroup:r,switchLayoutGroup:G.useContext(b0),isPresent:l,safeToRemove:u})}const Ix={borderRadius:{...rl,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:rl,borderTopRightRadius:rl,borderBottomLeftRadius:rl,borderBottomRightRadius:rl,boxShadow:Wx};function t2(a,l,u){const r=ne(a)?a:ai(a);return r.start(Pc("",r,l,u)),r.animation}const e2=(a,l)=>a.depth-l.depth;class n2{constructor(){this.children=[],this.isDirty=!1}add(l){gc(this.children,l),this.isDirty=!0}remove(l){vc(this.children,l),this.isDirty=!0}forEach(l){this.isDirty&&this.children.sort(e2),this.isDirty=!1,this.children.forEach(l)}}function a2(a,l){const u=ce.now(),r=({timestamp:c})=>{const d=c-u;d>=l&&(qn(r),a(d-l))};return Rt.setup(r,!0),()=>qn(r)}const G0=["TopLeft","TopRight","BottomLeft","BottomRight"],i2=G0.length,Kp=a=>typeof a=="string"?parseFloat(a):a,kp=a=>typeof a=="number"||it.test(a);function l2(a,l,u,r,c,d){c?(a.opacity=Ct(0,u.opacity??1,s2(r)),a.opacityExit=Ct(l.opacity??1,0,u2(r))):d&&(a.opacity=Ct(l.opacity??1,u.opacity??1,r));for(let f=0;f<i2;f++){const m=`border${G0[f]}Radius`;let y=Pp(l,m),p=Pp(u,m);if(y===void 0&&p===void 0)continue;y||(y=0),p||(p=0),y===0||p===0||kp(y)===kp(p)?(a[m]=Math.max(Ct(Kp(y),Kp(p),r),0),(Pe.test(p)||Pe.test(y))&&(a[m]+="%")):a[m]=p}(l.rotate||u.rotate)&&(a.rotate=Ct(l.rotate||0,u.rotate||0,r))}function Pp(a,l){return a[l]!==void 0?a[l]:a.borderRadius}const s2=X0(0,.5,jy),u2=X0(.5,.95,Ve);function X0(a,l,u){return r=>r<a?0:r>l?1:u(pl(a,l,r))}function Jp(a,l){a.min=l.min,a.max=l.max}function _e(a,l){Jp(a.x,l.x),Jp(a.y,l.y)}function Fp(a,l){a.translate=l.translate,a.scale=l.scale,a.originPoint=l.originPoint,a.origin=l.origin}function Wp(a,l,u,r,c){return a-=l,a=eu(a,1/u,r),c!==void 0&&(a=eu(a,1/c,r)),a}function r2(a,l=0,u=1,r=.5,c,d=a,f=a){if(Pe.test(l)&&(l=parseFloat(l),l=Ct(f.min,f.max,l/100)-f.min),typeof l!="number")return;let m=Ct(d.min,d.max,r);a===d&&(m-=l),a.min=Wp(a.min,l,u,m,c),a.max=Wp(a.max,l,u,m,c)}function $p(a,l,[u,r,c],d,f){r2(a,l[u],l[r],l[c],l.scale,d,f)}const o2=["x","scaleX","originX"],c2=["y","scaleY","originY"];function Ip(a,l,u,r){$p(a.x,l,o2,u?u.x:void 0,r?r.x:void 0),$p(a.y,l,c2,u?u.y:void 0,r?r.y:void 0)}function ty(a){return a.translate===0&&a.scale===1}function Z0(a){return ty(a.x)&&ty(a.y)}function ey(a,l){return a.min===l.min&&a.max===l.max}function f2(a,l){return ey(a.x,l.x)&&ey(a.y,l.y)}function ny(a,l){return Math.round(a.min)===Math.round(l.min)&&Math.round(a.max)===Math.round(l.max)}function Q0(a,l){return ny(a.x,l.x)&&ny(a.y,l.y)}function ay(a){return ie(a.x)/ie(a.y)}function iy(a,l){return a.translate===l.translate&&a.scale===l.scale&&a.originPoint===l.originPoint}class h2{constructor(){this.members=[]}add(l){gc(this.members,l),l.scheduleRender()}remove(l){if(vc(this.members,l),l===this.prevLead&&(this.prevLead=void 0),l===this.lead){const u=this.members[this.members.length-1];u&&this.promote(u)}}relegate(l){const u=this.members.findIndex(c=>l===c);if(u===0)return!1;let r;for(let c=u;c>=0;c--){const d=this.members[c];if(d.isPresent!==!1){r=d;break}}return r?(this.promote(r),!0):!1}promote(l,u){const r=this.lead;if(l!==r&&(this.prevLead=r,this.lead=l,l.show(),r)){r.instance&&r.scheduleRender(),l.scheduleRender(),l.resumeFrom=r,u&&(l.resumeFrom.preserveOpacity=!0),r.snapshot&&(l.snapshot=r.snapshot,l.snapshot.latestValues=r.animationValues||r.latestValues),l.root&&l.root.isUpdating&&(l.isLayoutDirty=!0);const{crossfade:c}=l.options;c===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(l=>{const{options:u,resumingFrom:r}=l;u.onExitComplete&&u.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(l=>{l.instance&&l.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function d2(a,l,u){let r="";const c=a.x.translate/l.x,d=a.y.translate/l.y,f=u?.z||0;if((c||d||f)&&(r=`translate3d(${c}px, ${d}px, ${f}px) `),(l.x!==1||l.y!==1)&&(r+=`scale(${1/l.x}, ${1/l.y}) `),u){const{transformPerspective:p,rotate:v,rotateX:b,rotateY:T,skewX:C,skewY:j}=u;p&&(r=`perspective(${p}px) ${r}`),v&&(r+=`rotate(${v}deg) `),b&&(r+=`rotateX(${b}deg) `),T&&(r+=`rotateY(${T}deg) `),C&&(r+=`skewX(${C}deg) `),j&&(r+=`skewY(${j}deg) `)}const m=a.x.scale*l.x,y=a.y.scale*l.y;return(m!==1||y!==1)&&(r+=`scale(${m}, ${y})`),r||"none"}const qo=["","X","Y","Z"],m2=1e3;let p2=0;function Yo(a,l,u,r){const{latestValues:c}=l;c[a]&&(u[a]=c[a],l.setStaticValue(a,0),r&&(r[a]=0))}function K0(a){if(a.hasCheckedOptimisedAppear=!0,a.root===a)return;const{visualElement:l}=a.options;if(!l)return;const u=O0(l);if(window.MotionHasOptimisedAnimation(u,"transform")){const{layout:c,layoutId:d}=a.options;window.MotionCancelOptimisedAnimation(u,"transform",Rt,!(c||d))}const{parent:r}=a;r&&!r.hasCheckedOptimisedAppear&&K0(r)}function k0({attachResizeListener:a,defaultParent:l,measureScroll:u,checkIsScrollRoot:r,resetTransform:c}){return class{constructor(f={},m=l?.()){this.id=p2++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(v2),this.nodes.forEach(x2),this.nodes.forEach(A2),this.nodes.forEach(b2)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=f,this.root=m?m.root||m:this,this.path=m?[...m.path,m]:[],this.parent=m,this.depth=m?m.depth+1:0;for(let y=0;y<this.path.length;y++)this.path[y].shouldResetTransform=!0;this.root===this&&(this.nodes=new n2)}addEventListener(f,m){return this.eventHandlers.has(f)||this.eventHandlers.set(f,new Tc),this.eventHandlers.get(f).add(m)}notifyListeners(f,...m){const y=this.eventHandlers.get(f);y&&y.notify(...m)}hasListeners(f){return this.eventHandlers.has(f)}mount(f){if(this.instance)return;this.isSVG=d0(f)&&!hT(f),this.instance=f;const{layoutId:m,layout:y,visualElement:p}=this.options;if(p&&!p.current&&p.mount(f),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(y||m)&&(this.isLayoutDirty=!0),a){let v,b=0;const T=()=>this.root.updateBlockedByResize=!1;Rt.read(()=>{b=window.innerWidth}),a(f,()=>{const C=window.innerWidth;C!==b&&(b=C,this.root.updateBlockedByResize=!0,v&&v(),v=a2(T,250),Ps.hasAnimatedSinceResize&&(Ps.hasAnimatedSinceResize=!1,this.nodes.forEach(uy)))})}m&&this.root.registerSharedNode(m,this),this.options.animate!==!1&&p&&(m||y)&&this.addEventListener("didUpdate",({delta:v,hasLayoutChanged:b,hasRelativeLayoutChanged:T,layout:C})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const j=this.options.transition||p.getDefaultTransition()||O2,{onLayoutAnimationStart:L,onLayoutAnimationComplete:Z}=p.getProps(),Y=!this.targetLayout||!Q0(this.targetLayout,C),Q=!b&&T;if(this.options.layoutRoot||this.resumeFrom||Q||b&&(Y||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const q={...jc(j,"layout"),onPlay:L,onComplete:Z};(p.shouldReduceMotion||this.options.layoutRoot)&&(q.delay=0,q.type=!1),this.startAnimation(q),this.setAnimationOrigin(v,Q)}else b||uy(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=C})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const f=this.getStack();f&&f.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),qn(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(E2),this.animationId++)}getTransformTemplate(){const{visualElement:f}=this.options;return f&&f.getProps().transformTemplate}willUpdate(f=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&K0(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let v=0;v<this.path.length;v++){const b=this.path[v];b.shouldResetTransform=!0,b.updateScroll("snapshot"),b.options.layoutRoot&&b.willUpdate(!1)}const{layoutId:m,layout:y}=this.options;if(m===void 0&&!y)return;const p=this.getTransformTemplate();this.prevTransformTemplateValue=p?p(this.latestValues,""):void 0,this.updateSnapshot(),f&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(ly);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(sy);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(T2),this.nodes.forEach(y2),this.nodes.forEach(g2)):this.nodes.forEach(sy),this.clearAllSnapshots();const m=ce.now();$t.delta=hn(0,1e3/60,m-$t.timestamp),$t.timestamp=m,$t.isProcessing=!0,No.update.process($t),No.preRender.process($t),No.render.process($t),$t.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Bc.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(S2),this.sharedNodes.forEach(M2)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Rt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Rt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!ie(this.snapshot.measuredBox.x)&&!ie(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let y=0;y<this.path.length;y++)this.path[y].updateScroll();const f=this.layout;this.layout=this.measure(!1),this.layoutCorrected=Ut(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:m}=this.options;m&&m.notify("LayoutMeasure",this.layout.layoutBox,f?f.layoutBox:void 0)}updateScroll(f="measure"){let m=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===f&&(m=!1),m&&this.instance){const y=r(this.instance);this.scroll={animationId:this.root.animationId,phase:f,isRoot:y,offset:u(this.instance),wasRoot:this.scroll?this.scroll.isRoot:y}}}resetTransform(){if(!c)return;const f=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,m=this.projectionDelta&&!Z0(this.projectionDelta),y=this.getTransformTemplate(),p=y?y(this.latestValues,""):void 0,v=p!==this.prevTransformTemplateValue;f&&this.instance&&(m||ra(this.latestValues)||v)&&(c(this.instance,p),this.shouldResetTransform=!1,this.scheduleRender())}measure(f=!0){const m=this.measurePageBox();let y=this.removeElementScroll(m);return f&&(y=this.removeTransform(y)),C2(y),{animationId:this.root.animationId,measuredBox:m,layoutBox:y,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:f}=this.options;if(!f)return Ut();const m=f.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(_2))){const{scroll:p}=this.root;p&&(ei(m.x,p.offset.x),ei(m.y,p.offset.y))}return m}removeElementScroll(f){const m=Ut();if(_e(m,f),this.scroll?.wasRoot)return m;for(let y=0;y<this.path.length;y++){const p=this.path[y],{scroll:v,options:b}=p;p!==this.root&&v&&b.layoutScroll&&(v.wasRoot&&_e(m,f),ei(m.x,v.offset.x),ei(m.y,v.offset.y))}return m}applyTransform(f,m=!1){const y=Ut();_e(y,f);for(let p=0;p<this.path.length;p++){const v=this.path[p];!m&&v.options.layoutScroll&&v.scroll&&v!==v.root&&ni(y,{x:-v.scroll.offset.x,y:-v.scroll.offset.y}),ra(v.latestValues)&&ni(y,v.latestValues)}return ra(this.latestValues)&&ni(y,this.latestValues),y}removeTransform(f){const m=Ut();_e(m,f);for(let y=0;y<this.path.length;y++){const p=this.path[y];if(!p.instance||!ra(p.latestValues))continue;uc(p.latestValues)&&p.updateSnapshot();const v=Ut(),b=p.measurePageBox();_e(v,b),Ip(m,p.latestValues,p.snapshot?p.snapshot.layoutBox:void 0,v)}return ra(this.latestValues)&&Ip(m,this.latestValues),m}setTargetDelta(f){this.targetDelta=f,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(f){this.options={...this.options,...f,crossfade:f.crossfade!==void 0?f.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==$t.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(f=!1){const m=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=m.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=m.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=m.isSharedProjectionDirty);const y=!!this.resumingFrom||this!==m;if(!(f||y&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:v,layoutId:b}=this.options;if(!(!this.layout||!(v||b))){if(this.resolvedRelativeTargetAt=$t.timestamp,!this.targetDelta&&!this.relativeTarget){const T=this.getClosestProjectingParent();T&&T.layout&&this.animationProgress!==1?(this.relativeParent=T,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Ut(),this.relativeTargetOrigin=Ut(),ml(this.relativeTargetOrigin,this.layout.layoutBox,T.layout.layoutBox),_e(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=Ut(),this.targetWithTransforms=Ut()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),wx(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):_e(this.target,this.layout.layoutBox),U0(this.target,this.targetDelta)):_e(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const T=this.getClosestProjectingParent();T&&!!T.resumingFrom==!!this.resumingFrom&&!T.options.layoutScroll&&T.target&&this.animationProgress!==1?(this.relativeParent=T,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Ut(),this.relativeTargetOrigin=Ut(),ml(this.relativeTargetOrigin,this.target,T.target),_e(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||uc(this.parent.latestValues)||j0(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const f=this.getLead(),m=!!this.resumingFrom||this!==f;let y=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(y=!1),m&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(y=!1),this.resolvedRelativeTargetAt===$t.timestamp&&(y=!1),y)return;const{layout:p,layoutId:v}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(p||v))return;_e(this.layoutCorrected,this.layout.layoutBox);const b=this.treeScale.x,T=this.treeScale.y;jx(this.layoutCorrected,this.treeScale,this.path,m),f.layout&&!f.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(f.target=f.layout.layoutBox,f.targetWithTransforms=Ut());const{target:C}=f;if(!C){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Fp(this.prevProjectionDelta.x,this.projectionDelta.x),Fp(this.prevProjectionDelta.y,this.projectionDelta.y)),dl(this.projectionDelta,this.layoutCorrected,C,this.latestValues),(this.treeScale.x!==b||this.treeScale.y!==T||!iy(this.projectionDelta.x,this.prevProjectionDelta.x)||!iy(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",C))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(f=!0){if(this.options.visualElement?.scheduleRender(),f){const m=this.getStack();m&&m.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ti(),this.projectionDelta=ti(),this.projectionDeltaWithTransform=ti()}setAnimationOrigin(f,m=!1){const y=this.snapshot,p=y?y.latestValues:{},v={...this.latestValues},b=ti();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!m;const T=Ut(),C=y?y.source:void 0,j=this.layout?this.layout.source:void 0,L=C!==j,Z=this.getStack(),Y=!Z||Z.members.length<=1,Q=!!(L&&!Y&&this.options.crossfade===!0&&!this.path.some(R2));this.animationProgress=0;let q;this.mixTargetDelta=tt=>{const H=tt/1e3;ry(b.x,f.x,H),ry(b.y,f.y,H),this.setTargetDelta(b),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(ml(T,this.layout.layoutBox,this.relativeParent.layout.layoutBox),D2(this.relativeTarget,this.relativeTargetOrigin,T,H),q&&f2(this.relativeTarget,q)&&(this.isProjectionDirty=!1),q||(q=Ut()),_e(q,this.relativeTarget)),L&&(this.animationValues=v,l2(v,p,this.latestValues,H,Q,Y)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=H},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(f){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(qn(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Rt.update(()=>{Ps.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=ai(0)),this.currentAnimation=t2(this.motionValue,[0,1e3],{...f,velocity:0,isSync:!0,onUpdate:m=>{this.mixTargetDelta(m),f.onUpdate&&f.onUpdate(m)},onStop:()=>{},onComplete:()=>{f.onComplete&&f.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const f=this.getStack();f&&f.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(m2),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const f=this.getLead();let{targetWithTransforms:m,target:y,layout:p,latestValues:v}=f;if(!(!m||!y||!p)){if(this!==f&&this.layout&&p&&P0(this.options.animationType,this.layout.layoutBox,p.layoutBox)){y=this.target||Ut();const b=ie(this.layout.layoutBox.x);y.x.min=f.target.x.min,y.x.max=y.x.min+b;const T=ie(this.layout.layoutBox.y);y.y.min=f.target.y.min,y.y.max=y.y.min+T}_e(m,y),ni(m,v),dl(this.projectionDeltaWithTransform,this.layoutCorrected,m,v)}}registerSharedNode(f,m){this.sharedNodes.has(f)||this.sharedNodes.set(f,new h2),this.sharedNodes.get(f).add(m);const p=m.options.initialPromotionConfig;m.promote({transition:p?p.transition:void 0,preserveFollowOpacity:p&&p.shouldPreserveFollowOpacity?p.shouldPreserveFollowOpacity(m):void 0})}isLead(){const f=this.getStack();return f?f.lead===this:!0}getLead(){const{layoutId:f}=this.options;return f?this.getStack()?.lead||this:this}getPrevLead(){const{layoutId:f}=this.options;return f?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:f}=this.options;if(f)return this.root.sharedNodes.get(f)}promote({needsReset:f,transition:m,preserveFollowOpacity:y}={}){const p=this.getStack();p&&p.promote(this,y),f&&(this.projectionDelta=void 0,this.needsReset=!0),m&&this.setOptions({transition:m})}relegate(){const f=this.getStack();return f?f.relegate(this):!1}resetSkewAndRotation(){const{visualElement:f}=this.options;if(!f)return;let m=!1;const{latestValues:y}=f;if((y.z||y.rotate||y.rotateX||y.rotateY||y.rotateZ||y.skewX||y.skewY)&&(m=!0),!m)return;const p={};y.z&&Yo("z",f,p,this.animationValues);for(let v=0;v<qo.length;v++)Yo(`rotate${qo[v]}`,f,p,this.animationValues),Yo(`skew${qo[v]}`,f,p,this.animationValues);f.render();for(const v in p)f.setStaticValue(v,p[v]),this.animationValues&&(this.animationValues[v]=p[v]);f.scheduleRender()}applyProjectionStyles(f,m){if(!this.instance||this.isSVG)return;if(!this.isVisible){f.visibility="hidden";return}const y=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,f.visibility="",f.opacity="",f.pointerEvents=ks(m?.pointerEvents)||"",f.transform=y?y(this.latestValues,""):"none";return}const p=this.getLead();if(!this.projectionDelta||!this.layout||!p.target){this.options.layoutId&&(f.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,f.pointerEvents=ks(m?.pointerEvents)||""),this.hasProjected&&!ra(this.latestValues)&&(f.transform=y?y({},""):"none",this.hasProjected=!1);return}f.visibility="";const v=p.animationValues||p.latestValues;this.applyTransformsToTarget();let b=d2(this.projectionDeltaWithTransform,this.treeScale,v);y&&(b=y(v,b)),f.transform=b;const{x:T,y:C}=this.projectionDelta;f.transformOrigin=`${T.origin*100}% ${C.origin*100}% 0`,p.animationValues?f.opacity=p===this?v.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:v.opacityExit:f.opacity=p===this?v.opacity!==void 0?v.opacity:"":v.opacityExit!==void 0?v.opacityExit:0;for(const j in bl){if(v[j]===void 0)continue;const{correct:L,applyTo:Z,isCSSVariable:Y}=bl[j],Q=b==="none"?v[j]:L(v[j],p);if(Z){const q=Z.length;for(let tt=0;tt<q;tt++)f[Z[tt]]=Q}else Y?this.options.visualElement.renderState.vars[j]=Q:f[j]=Q}this.options.layoutId&&(f.pointerEvents=p===this?ks(m?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(f=>f.currentAnimation?.stop()),this.root.nodes.forEach(ly),this.root.sharedNodes.clear()}}}function y2(a){a.updateLayout()}function g2(a){const l=a.resumeFrom?.snapshot||a.snapshot;if(a.isLead()&&a.layout&&l&&a.hasListeners("didUpdate")){const{layoutBox:u,measuredBox:r}=a.layout,{animationType:c}=a.options,d=l.source!==a.layout.source;c==="size"?Ne(v=>{const b=d?l.measuredBox[v]:l.layoutBox[v],T=ie(b);b.min=u[v].min,b.max=b.min+T}):P0(c,l.layoutBox,u)&&Ne(v=>{const b=d?l.measuredBox[v]:l.layoutBox[v],T=ie(u[v]);b.max=b.min+T,a.relativeTarget&&!a.currentAnimation&&(a.isProjectionDirty=!0,a.relativeTarget[v].max=a.relativeTarget[v].min+T)});const f=ti();dl(f,u,l.layoutBox);const m=ti();d?dl(m,a.applyTransform(r,!0),l.measuredBox):dl(m,u,l.layoutBox);const y=!Z0(f);let p=!1;if(!a.resumeFrom){const v=a.getClosestProjectingParent();if(v&&!v.resumeFrom){const{snapshot:b,layout:T}=v;if(b&&T){const C=Ut();ml(C,l.layoutBox,b.layoutBox);const j=Ut();ml(j,u,T.layoutBox),Q0(C,j)||(p=!0),v.options.layoutRoot&&(a.relativeTarget=j,a.relativeTargetOrigin=C,a.relativeParent=v)}}}a.notifyListeners("didUpdate",{layout:u,snapshot:l,delta:m,layoutDelta:f,hasLayoutChanged:y,hasRelativeLayoutChanged:p})}else if(a.isLead()){const{onExitComplete:u}=a.options;u&&u()}a.options.transition=void 0}function v2(a){a.parent&&(a.isProjecting()||(a.isProjectionDirty=a.parent.isProjectionDirty),a.isSharedProjectionDirty||(a.isSharedProjectionDirty=!!(a.isProjectionDirty||a.parent.isProjectionDirty||a.parent.isSharedProjectionDirty)),a.isTransformDirty||(a.isTransformDirty=a.parent.isTransformDirty))}function b2(a){a.isProjectionDirty=a.isSharedProjectionDirty=a.isTransformDirty=!1}function S2(a){a.clearSnapshot()}function ly(a){a.clearMeasurements()}function sy(a){a.isLayoutDirty=!1}function T2(a){const{visualElement:l}=a.options;l&&l.getProps().onBeforeLayoutMeasure&&l.notify("BeforeLayoutMeasure"),a.resetTransform()}function uy(a){a.finishAnimation(),a.targetDelta=a.relativeTarget=a.target=void 0,a.isProjectionDirty=!0}function x2(a){a.resolveTargetDelta()}function A2(a){a.calcProjection()}function E2(a){a.resetSkewAndRotation()}function M2(a){a.removeLeadSnapshot()}function ry(a,l,u){a.translate=Ct(l.translate,0,u),a.scale=Ct(l.scale,1,u),a.origin=l.origin,a.originPoint=l.originPoint}function oy(a,l,u,r){a.min=Ct(l.min,u.min,r),a.max=Ct(l.max,u.max,r)}function D2(a,l,u,r){oy(a.x,l.x,u.x,r),oy(a.y,l.y,u.y,r)}function R2(a){return a.animationValues&&a.animationValues.opacityExit!==void 0}const O2={duration:.45,ease:[.4,0,.1,1]},cy=a=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(a),fy=cy("applewebkit/")&&!cy("chrome/")?Math.round:Ve;function hy(a){a.min=fy(a.min),a.max=fy(a.max)}function C2(a){hy(a.x),hy(a.y)}function P0(a,l,u){return a==="position"||a==="preserve-aspect"&&!zx(ay(l),ay(u),.2)}function _2(a){return a!==a.root&&a.scroll?.wasRoot}const N2=k0({attachResizeListener:(a,l)=>Tl(a,"resize",l),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Go={current:void 0},J0=k0({measureScroll:a=>({x:a.scrollLeft,y:a.scrollTop}),defaultParent:()=>{if(!Go.current){const a=new N2({});a.mount(window),a.setOptions({layoutScroll:!0}),Go.current=a}return Go.current},resetTransform:(a,l)=>{a.style.transform=l!==void 0?l:"none"},checkIsScrollRoot:a=>window.getComputedStyle(a).position==="fixed"}),V2={pan:{Feature:Fx},drag:{Feature:Jx,ProjectionNode:J0,MeasureLayout:Y0}};function dy(a,l,u){const{props:r}=a;a.animationState&&r.whileHover&&a.animationState.setActive("whileHover",u==="Start");const c="onHover"+u,d=r[c];d&&Rt.postRender(()=>d(l,Ml(l)))}class z2 extends Gn{mount(){const{current:l}=this.node;l&&(this.unmount=uT(l,(u,r)=>(dy(this.node,r,"Start"),c=>dy(this.node,c,"End"))))}unmount(){}}class w2 extends Gn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let l=!1;try{l=this.node.current.matches(":focus-visible")}catch{l=!0}!l||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=xl(Tl(this.node.current,"focus",()=>this.onFocus()),Tl(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function my(a,l,u){const{props:r}=a;if(a.current instanceof HTMLButtonElement&&a.current.disabled)return;a.animationState&&r.whileTap&&a.animationState.setActive("whileTap",u==="Start");const c="onTap"+(u==="End"?"":u),d=r[c];d&&Rt.postRender(()=>d(l,Ml(l)))}class j2 extends Gn{mount(){const{current:l}=this.node;l&&(this.unmount=fT(l,(u,r)=>(my(this.node,r,"Start"),(c,{success:d})=>my(this.node,c,d?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const cc=new WeakMap,Xo=new WeakMap,U2=a=>{const l=cc.get(a.target);l&&l(a)},B2=a=>{a.forEach(U2)};function L2({root:a,...l}){const u=a||document;Xo.has(u)||Xo.set(u,{});const r=Xo.get(u),c=JSON.stringify(l);return r[c]||(r[c]=new IntersectionObserver(B2,{root:a,...l})),r[c]}function H2(a,l,u){const r=L2(l);return cc.set(a,u),r.observe(a),()=>{cc.delete(a),r.unobserve(a)}}const q2={some:0,all:1};class Y2 extends Gn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:l={}}=this.node.getProps(),{root:u,margin:r,amount:c="some",once:d}=l,f={root:u?u.current:void 0,rootMargin:r,threshold:typeof c=="number"?c:q2[c]},m=y=>{const{isIntersecting:p}=y;if(this.isInView===p||(this.isInView=p,d&&!p&&this.hasEnteredView))return;p&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",p);const{onViewportEnter:v,onViewportLeave:b}=this.node.getProps(),T=p?v:b;T&&T(y)};return H2(this.node.current,f,m)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:l,prevProps:u}=this.node;["amount","margin","root"].some(G2(l,u))&&this.startObserver()}unmount(){}}function G2({viewport:a={}},{viewport:l={}}={}){return u=>a[u]!==l[u]}const X2={inView:{Feature:Y2},tap:{Feature:j2},focus:{Feature:w2},hover:{Feature:z2}},Z2={layout:{ProjectionNode:J0,MeasureLayout:Y0}},fc={current:null},F0={current:!1};function Q2(){if(F0.current=!0,!!yc)if(window.matchMedia){const a=window.matchMedia("(prefers-reduced-motion)"),l=()=>fc.current=a.matches;a.addEventListener("change",l),l()}else fc.current=!1}const K2=new WeakMap;function k2(a,l,u){for(const r in l){const c=l[r],d=u[r];if(ne(c))a.addValue(r,c);else if(ne(d))a.addValue(r,ai(c,{owner:a}));else if(d!==c)if(a.hasValue(r)){const f=a.getValue(r);f.liveStyle===!0?f.jump(c):f.hasAnimated||f.set(c)}else{const f=a.getStaticValue(r);a.addValue(r,ai(f!==void 0?f:c,{owner:a}))}}for(const r in u)l[r]===void 0&&a.removeValue(r);return l}const py=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class P2{scrapeMotionValuesFromProps(l,u,r){return{}}constructor({parent:l,props:u,presenceContext:r,reducedMotionConfig:c,blockInitialAnimation:d,visualState:f},m={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=zc,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const T=ce.now();this.renderScheduledAt<T&&(this.renderScheduledAt=T,Rt.render(this.render,!1,!0))};const{latestValues:y,renderState:p}=f;this.latestValues=y,this.baseTarget={...y},this.initialValues=u.initial?{...y}:{},this.renderState=p,this.parent=l,this.props=u,this.presenceContext=r,this.depth=l?l.depth+1:0,this.reducedMotionConfig=c,this.options=m,this.blockInitialAnimation=!!d,this.isControllingVariants=lu(u),this.isVariantNode=g0(u),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(l&&l.current);const{willChange:v,...b}=this.scrapeMotionValuesFromProps(u,{},this);for(const T in b){const C=b[T];y[T]!==void 0&&ne(C)&&C.set(y[T],!1)}}mount(l){this.current=l,K2.set(l,this),this.projection&&!this.projection.instance&&this.projection.mount(l),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((u,r)=>this.bindToMotionValue(r,u)),F0.current||Q2(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:fc.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),qn(this.notifyUpdate),qn(this.render),this.valueSubscriptions.forEach(l=>l()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const l in this.events)this.events[l].clear();for(const l in this.features){const u=this.features[l];u&&(u.unmount(),u.isMounted=!1)}this.current=null}bindToMotionValue(l,u){this.valueSubscriptions.has(l)&&this.valueSubscriptions.get(l)();const r=ui.has(l);r&&this.onBindTransform&&this.onBindTransform();const c=u.on("change",m=>{this.latestValues[l]=m,this.props.onUpdate&&Rt.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),d=u.on("renderRequest",this.scheduleRender);let f;window.MotionCheckAppearSync&&(f=window.MotionCheckAppearSync(this,l,u)),this.valueSubscriptions.set(l,()=>{c(),d(),f&&f(),u.owner&&u.stop()})}sortNodePosition(l){return!this.current||!this.sortInstanceNodePosition||this.type!==l.type?0:this.sortInstanceNodePosition(this.current,l.current)}updateFeatures(){let l="animation";for(l in ii){const u=ii[l];if(!u)continue;const{isEnabled:r,Feature:c}=u;if(!this.features[l]&&c&&r(this.props)&&(this.features[l]=new c(this)),this.features[l]){const d=this.features[l];d.isMounted?d.update():(d.mount(),d.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):Ut()}getStaticValue(l){return this.latestValues[l]}setStaticValue(l,u){this.latestValues[l]=u}update(l,u){(l.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=l,this.prevPresenceContext=this.presenceContext,this.presenceContext=u;for(let r=0;r<py.length;r++){const c=py[r];this.propEventSubscriptions[c]&&(this.propEventSubscriptions[c](),delete this.propEventSubscriptions[c]);const d="on"+c,f=l[d];f&&(this.propEventSubscriptions[c]=this.on(c,f))}this.prevMotionValues=k2(this,this.scrapeMotionValuesFromProps(l,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(l){return this.props.variants?this.props.variants[l]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(l){const u=this.getClosestVariantNode();if(u)return u.variantChildren&&u.variantChildren.add(l),()=>u.variantChildren.delete(l)}addValue(l,u){const r=this.values.get(l);u!==r&&(r&&this.removeValue(l),this.bindToMotionValue(l,u),this.values.set(l,u),this.latestValues[l]=u.get())}removeValue(l){this.values.delete(l);const u=this.valueSubscriptions.get(l);u&&(u(),this.valueSubscriptions.delete(l)),delete this.latestValues[l],this.removeValueFromRenderState(l,this.renderState)}hasValue(l){return this.values.has(l)}getValue(l,u){if(this.props.values&&this.props.values[l])return this.props.values[l];let r=this.values.get(l);return r===void 0&&u!==void 0&&(r=ai(u===null?void 0:u,{owner:this}),this.addValue(l,r)),r}readValue(l,u){let r=this.latestValues[l]!==void 0||!this.current?this.latestValues[l]:this.getBaseTargetFromProps(this.props,l)??this.readValueFromInstance(this.current,l,this.options);return r!=null&&(typeof r=="string"&&(My(r)||Ry(r))?r=parseFloat(r):!mT(r)&&Yn.test(u)&&(r=r0(l,u)),this.setBaseTarget(l,ne(r)?r.get():r)),ne(r)?r.get():r}setBaseTarget(l,u){this.baseTarget[l]=u}getBaseTarget(l){const{initial:u}=this.props;let r;if(typeof u=="string"||typeof u=="object"){const d=Kc(this.props,u,this.presenceContext?.custom);d&&(r=d[l])}if(u&&r!==void 0)return r;const c=this.getBaseTargetFromProps(this.props,l);return c!==void 0&&!ne(c)?c:this.initialValues[l]!==void 0&&r===void 0?void 0:this.baseTarget[l]}on(l,u){return this.events[l]||(this.events[l]=new Tc),this.events[l].add(u)}notify(l,...u){this.events[l]&&this.events[l].notify(...u)}}class W0 extends P2{constructor(){super(...arguments),this.KeyframeResolver=nT}sortInstanceNodePosition(l,u){return l.compareDocumentPosition(u)&2?1:-1}getBaseTargetFromProps(l,u){return l.style?l.style[u]:void 0}removeValueFromRenderState(l,{vars:u,style:r}){delete u[l],delete r[l]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:l}=this.props;ne(l)&&(this.childSubscription=l.on("change",u=>{this.current&&(this.current.textContent=`${u}`)}))}}function $0(a,{style:l,vars:u},r,c){const d=a.style;let f;for(f in l)d[f]=l[f];c?.applyProjectionStyles(d,r);for(f in u)d.setProperty(f,u[f])}function J2(a){return window.getComputedStyle(a)}class F2 extends W0{constructor(){super(...arguments),this.type="html",this.renderInstance=$0}readValueFromInstance(l,u){if(ui.has(u))return this.projection?.isProjecting?$o(u):TS(l,u);{const r=J2(l),c=(Ec(u)?r.getPropertyValue(u):r[u])||0;return typeof c=="string"?c.trim():c}}measureInstanceViewportBox(l,{transformPagePoint:u}){return B0(l,u)}build(l,u,r){Xc(l,u,r.transformTemplate)}scrapeMotionValuesFromProps(l,u,r){return kc(l,u,r)}}const I0=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function W2(a,l,u,r){$0(a,l,void 0,r);for(const c in l.attrs)a.setAttribute(I0.has(c)?c:Gc(c),l.attrs[c])}class $2 extends W0{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Ut}getBaseTargetFromProps(l,u){return l[u]}readValueFromInstance(l,u){if(ui.has(u)){const r=u0(u);return r&&r.default||0}return u=I0.has(u)?u:Gc(u),l.getAttribute(u)}scrapeMotionValuesFromProps(l,u,r){return R0(l,u,r)}build(l,u,r){A0(l,u,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(l,u,r,c){W2(l,u,r,c)}mount(l){this.isSVGTag=M0(l.tagName),super.mount(l)}}const I2=(a,l)=>Qc(a)?new $2(l):new F2(l,{allowProjection:a!==G.Fragment}),tA=IT({...Mx,...X2,...V2,...Z2},I2),Be=ET(tA),Js=({variant:a="primary",children:l,className:u="",href:r,...c})=>{const m=`inline-flex items-center justify-center rounded-md font-medium transition-all duration-300 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 ${{primary:"bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500 px-6 py-3",secondary:"bg-secondary-500 hover:bg-secondary-600 text-white focus:ring-secondary-400 px-6 py-3",outline:"border-2 border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-400 px-6 py-2.5"}[a]} ${u}`;return r?_.jsx("a",{href:r,className:m,target:c.target,rel:c.rel,children:l}):_.jsx("button",{className:m,...c,children:l})},eA=({isOpen:a,onClose:l,children:u})=>a?_.jsx(bT,{children:_.jsx(Be.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50",onClick:l,children:_.jsxs(Be.div,{initial:{scale:.95,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.95,opacity:0},className:"relative w-full max-w-4xl bg-white rounded-lg shadow-xl",onClick:r=>r.stopPropagation(),children:[_.jsx("button",{onClick:l,className:"absolute top-4 right-4 text-gray-500 hover:text-gray-700","aria-label":"Close modal",children:_.jsx(Ay,{size:24})}),_.jsx("div",{className:"h-[80vh] overflow-y-auto",children:u})]})})}):null,nA=()=>{const a=new Date("2025-11-19T08:00:00").getTime(),[l,u]=G.useState(!1),r=["/hero1.jpg","/hero2.jpg","/hero3.jpg","/hero4.JPG","/hero5.JPG","/hero6.jpg"],[c,d]=G.useState(0);G.useEffect(()=>{const m=setInterval(()=>{d(y=>{const p=y+1;return p>=r.length?0:p})},5e3);return()=>clearInterval(m)},[r.length]);const f=({days:m,hours:y,minutes:p,seconds:v,completed:b})=>b?_.jsx("span",{children:"Event has started!"}):_.jsxs("div",{className:"grid grid-cols-4 gap-2 md:gap-4 text-center",children:[_.jsxs("div",{className:"bg-white bg-opacity-90 rounded-lg p-2 md:p-4",children:[_.jsx("div",{className:"text-2xl md:text-4xl font-bold text-primary-700",children:m}),_.jsx("div",{className:"text-xs md:text-sm text-gray-600",children:"Days"})]}),_.jsxs("div",{className:"bg-white bg-opacity-90 rounded-lg p-2 md:p-4",children:[_.jsx("div",{className:"text-2xl md:text-4xl font-bold text-primary-700",children:y}),_.jsx("div",{className:"text-xs md:text-sm text-gray-600",children:"Hours"})]}),_.jsxs("div",{className:"bg-white bg-opacity-90 rounded-lg p-2 md:p-4",children:[_.jsx("div",{className:"text-2xl md:text-4xl font-bold text-primary-700",children:p}),_.jsx("div",{className:"text-xs md:text-sm text-gray-600",children:"Minutes"})]}),_.jsxs("div",{className:"bg-white bg-opacity-90 rounded-lg p-2 md:p-4",children:[_.jsx("div",{className:"text-2xl md:text-4xl font-bold text-primary-700",children:v}),_.jsx("div",{className:"text-xs md:text-sm text-gray-600",children:"Seconds"})]})]});return _.jsxs(_.Fragment,{children:[_.jsxs("section",{className:"relative min-h-screen flex items-center overflow-hidden py-20",children:[_.jsx("div",{className:"absolute inset-0",children:r.map((m,y)=>_.jsx(Be.div,{className:"absolute inset-0 bg-cover bg-center",style:{backgroundImage:`url(${m})`},initial:{opacity:0},animate:{opacity:y===c?1:0},transition:{duration:1.5,ease:"easeInOut"}},y))}),_.jsx("div",{className:"absolute inset-0 bg-black/70 backdrop-blur-[1px]"}),_.jsx("div",{className:"container mx-auto px-4 md:px-6 relative z-10",children:_.jsxs("div",{className:"max-w-4xl",children:[_.jsxs(Be.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.7},children:[_.jsx("h1",{className:"font-heading font-bold text-4xl md:text-5xl lg:text-6xl text-white mb-4",children:"West Africa Stablecoin Summit"}),_.jsx("h2",{className:"text-xl md:text-2xl text-white mb-8",children:"Enhancing Adoption of a Borderless Digital Economy."})]}),_.jsx(Be.div,{className:"mb-8",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3,duration:.7},children:_.jsx(dc,{date:a,renderer:f})}),_.jsxs(Be.div,{className:"flex items-center text-white mb-4",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5,duration:.7},children:[_.jsx(db,{className:"mr-2 h-5 w-5 text-secondary-400"}),_.jsx("span",{children:"Abuja, Nigeria"})]}),_.jsx(Be.div,{className:"flex items-center text-white mb-8",initial:{opacity:0},animate:{opacity:1},transition:{delay:.6,duration:.7},children:_.jsxs("div",{className:"flex gap-3 items-center",children:[_.jsxs("div",{className:"flex items-center",children:[_.jsx(I1,{className:"mr-2 h-5 w-5 text-secondary-400"}),_.jsx("span",{children:"November 19-20, 2025"})]}),_.jsxs("div",{className:"flex items-center",children:[_.jsx(eb,{className:"mr-2 h-5 w-5 text-secondary-400"}),_.jsx("span",{children:"8:00am WAT"})]})]})}),_.jsxs(Be.div,{className:"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.7,duration:.5},children:[_.jsx(Js,{variant:"primary",className:"bg-yellow-400 text-black hover:bg-yellow-500 border-none",href:"https://tix.africa/west-africa-stablecoin-summit",children:"Register Now"}),_.jsx(Js,{variant:"outline",className:"border-yellow-400 text-yellow-400 hover:bg-yellow-400 hover:text-black hover:border-yellow-500",onClick:()=>u(!0),children:"Apply to Sponsor"}),_.jsx(Js,{variant:"outline",className:"border-yellow-400 text-yellow-400 hover:bg-yellow-400 hover:text-black hover:border-yellow-500",href:"https://docs.google.com/forms/d/e/1FAIpQLScleRTYb0Jgy9KdnrCTx_0uQYsreg2yBcCVV8_Ltr6uFVAlqA/viewform?usp=header",target:"_blank",rel:"noopener noreferrer",children:"Apply to Speak"})]})]})})]}),_.jsx(eA,{isOpen:l,onClose:()=>u(!1),children:_.jsx("iframe",{src:"https://docs.google.com/forms/d/e/1FAIpQLSd7SzAjSvxA1lOkxH1nqBMkifmbxc3rwtSx_S1kurqFSmA6wA/viewform?embedded=true",width:"100%",height:"100%",frameBorder:"0",marginHeight:0,marginWidth:0,children:"Loading form..."})})]})},aA=()=>_.jsx("section",{className:"py-20 bg-gray-50",children:_.jsx("div",{className:"container mx-auto px-4 md:px-6",children:_.jsxs("div",{className:"grid md:grid-cols-2 gap-12 items-center",children:[_.jsxs(Be.div,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},viewport:{once:!0},transition:{duration:.7},className:"space-y-6",children:[_.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900",children:"About ASN"}),_.jsxs("div",{className:"space-y-4 text-gray-600",children:[_.jsx("p",{children:"The Africa Stablecoin Network is committed to advancing the adoption and integration of stablecoins across Africa. We believe that collaboration among stakeholders is essential for creating a thriving ecosystem that benefits all Africans."}),_.jsx("p",{children:"Our mission is to create a unified network for all stakeholders in the stablecoin industry, facilitating knowledge exchange, advocacy, and the development of robust infrastructures that support financial inclusion and economic growth."})]})]}),_.jsxs(Be.div,{initial:{opacity:0,x:20},whileInView:{opacity:1,x:0},viewport:{once:!0},transition:{duration:.7},className:"bg-white p-8 rounded-xl shadow-lg space-y-6",children:[_.jsx("h3",{className:"text-2xl font-bold text-gray-900",children:"Join Our Network"}),_.jsx("p",{className:"text-gray-600",children:"Become a part of Africa's leading stablecoin network and help shape the future of digital finance in Africa."}),_.jsx(Js,{variant:"primary",className:"w-full justify-center bg-yellow-400 text-black hover:bg-yellow-500 border-none",href:"https://docs.google.com/forms/d/1g76AMABlZ9U5EL8QodQjw4Rp1oHeJYXvqkkfiBdxx_Y/edit",target:"_blank",rel:"noopener noreferrer",children:"Become a Member"})]})]})})}),iA="/assets/logo-BqFg51mE.png",tg=({className:a="h-10"})=>_.jsx("div",{className:`flex items-center ${a}`,children:_.jsx("img",{src:iA,alt:"Africa Stablecoin Network Logo",className:"h-full w-auto mr-2"})}),lA=()=>{const[a,l]=G.useState(!1),u=()=>l(!a);return _.jsxs("header",{className:"fixed top-0 left-0 right-0 z-50 bg-white shadow-md py-3",children:[_.jsx("div",{className:"container mx-auto px-4 md:px-6",children:_.jsxs("div",{className:"flex justify-between items-center",children:[_.jsx("div",{className:"z-50",children:_.jsx(tg,{className:"h-12"})}),_.jsxs("div",{className:"hidden md:flex items-center space-x-4",children:[_.jsx("a",{href:"https://tix.africa/west-africa-stablecoin-summit",className:"px-5 py-2.5 rounded-md bg-yellow-400 text-black font-medium text-sm hover:bg-yellow-500 border-none transition-all transform hover:scale-105 duration-300",children:"Get Tickets"}),_.jsx("a",{href:"mailto:<EMAIL>?subject=Sponsorship%20Deck%20Request",className:"px-5 py-2.5 rounded-md border-2 border-yellow-400 text-yellow-400 font-medium text-sm hover:bg-yellow-400 hover:text-black hover:border-yellow-500 transition-all transform hover:scale-105 duration-300",children:"Request Sponsorship Deck"})]}),_.jsx("button",{className:"md:hidden text-dark-700 focus:outline-none",onClick:u,"aria-label":"Toggle menu",children:a?_.jsx(Ay,{className:"h-6 w-6"}):_.jsx(pb,{className:"h-6 w-6"})})]})}),_.jsx(Be.div,{className:`md:hidden ${a?"block":"hidden"}`,initial:{opacity:0,height:0},animate:{opacity:a?1:0,height:a?"auto":0},transition:{duration:.3},children:_.jsx("div",{className:"container mx-auto px-4 py-4 bg-white shadow-lg rounded-b-lg",children:_.jsxs("nav",{className:"flex flex-col space-y-4",children:[_.jsx("a",{href:"https://tix.africa/afristablecoin",className:"px-5 py-2.5 rounded-md bg-yellow-400 text-black font-medium text-center hover:bg-yellow-500 border-none transition-all",onClick:u,children:"Get Tickets"}),_.jsx("a",{href:"mailto:<EMAIL>?subject=Sponsorship%20Deck%20Request",className:"px-5 py-2.5 rounded-md border-2 border-yellow-400 text-yellow-400 font-medium text-center hover:bg-yellow-400 hover:text-black hover:border-yellow-500 transition-all",onClick:u,children:"Request Sponsorship Deck"})]})})})]})},sA=()=>_.jsx("footer",{id:"contact",className:"bg-slate-900 text-white pt-16 pb-8",children:_.jsxs("div",{className:"container-custom",children:[_.jsxs("div",{className:"grid md:grid-cols-2 gap-12 mb-12",children:[_.jsxs("div",{children:[_.jsx("div",{className:"mb-6",children:_.jsx(tg,{})}),_.jsx("p",{className:"text-slate-400 mb-6",children:"ASN is dedicated to advancing the adoption of stablecoins as a trusted medium for cross-border payments and financial inclusion across Africa."}),_.jsxs("div",{className:"flex space-x-4",children:[_.jsx("a",{href:"https://www.x.com/afristablecoin",target:"_blank",rel:"noopener noreferrer",className:"text-slate-400 hover:text-amber-500 transition-colors",children:_.jsx(gb,{className:"w-5 h-5"})}),_.jsx("a",{href:"https://www.linkedin.com/company/afristablecoin",target:"_blank",rel:"noopener noreferrer",className:"text-slate-400 hover:text-amber-500 transition-colors",children:_.jsx(ob,{className:"w-5 h-5"})}),_.jsx("a",{href:"https://www.facebook.com/afristablecoin",target:"_blank",rel:"noopener noreferrer",className:"text-slate-400 hover:text-amber-500 transition-colors",children:_.jsx(ab,{className:"w-5 h-5"})}),_.jsx("a",{href:"https://www.instagram.com/afristablecoin",target:"_blank",rel:"noopener noreferrer",className:"text-slate-400 hover:text-amber-500 transition-colors",children:_.jsx(ub,{className:"w-5 h-5"})})]})]}),_.jsxs("div",{children:[_.jsx("h3",{className:"text-xl font-bold mb-6",children:"Contact Us"}),_.jsxs("ul",{className:"space-y-4",children:[_.jsxs("li",{className:"flex items-center gap-3",children:[_.jsx(fb,{className:"w-5 h-5 text-amber-500"}),_.jsx("a",{href:"mailto:<EMAIL>",className:"text-slate-400 hover:text-amber-500 transition-colors",children:"events(@)afristablecoin.org"})]}),_.jsxs("li",{className:"flex items-center gap-3",children:[_.jsx(lb,{className:"w-5 h-5 text-amber-500"}),_.jsx("a",{href:"https://afristablecoin.org",target:"_blank",rel:"noopener noreferrer",className:"text-slate-400 hover:text-amber-500 transition-colors",children:"afristablecoin.org"})]}),_.jsx("li",{className:"text-slate-400",children:"+234 903 983 0751"})]})]})]}),_.jsxs("div",{className:"border-t border-slate-800 pt-8 mt-8",children:[_.jsx("div",{className:"flex flex-col md:flex-row justify-between items-center",children:_.jsxs("p",{className:"text-slate-500 mb-4 md:mb-0",children:["© ",new Date().getFullYear()," Africa Stablecoin Network. All rights reserved."]})}),_.jsx("p",{className:"text-center text-slate-500 mt-8 text-sm",children:"Event Hashtag: #LagosStablecoinBreakfast #StablecoinLeadership"})]})]})});function uA(){return _.jsxs("div",{className:"min-h-screen bg-white text-dark-700",children:[_.jsx(lA,{}),_.jsxs("main",{children:[_.jsx(nA,{}),_.jsx(aA,{})]}),_.jsx(sA,{})]})}V1.createRoot(document.getElementById("root")).render(_.jsx(G.StrictMode,{children:_.jsx(uA,{})}));
