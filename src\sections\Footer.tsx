import {
  Mail,
  Globe,
  Twitter,
  Linkedin,
  Facebook,
  Instagram,
} from "lucide-react";
import Logo from "../components/Logo";

const Footer = () => {
  return (
    <footer id="contact" className="bg-slate-900 text-white pt-16 pb-8">
      <div className="container-custom">
        <div className="grid md:grid-cols-2 gap-12 mb-12">
          <div>
            <div className="mb-6">
              <Logo />
            </div>
            <p className="text-slate-400 mb-6">
              ASN is dedicated to advancing the adoption of stablecoins as a
              trusted medium for cross-border payments and financial inclusion
              across Africa.
            </p>
            <div className="flex space-x-4">
              <a
                href="https://www.x.com/afristablecoin"
                target="_blank"
                rel="noopener noreferrer"
                className="text-slate-400 hover:text-amber-500 transition-colors"
              >
                <Twitter className="w-5 h-5" />
              </a>
              <a
                href="https://www.linkedin.com/company/afristablecoin"
                target="_blank"
                rel="noopener noreferrer"
                className="text-slate-400 hover:text-amber-500 transition-colors"
              >
                <Linkedin className="w-5 h-5" />
              </a>
              <a
                href="https://www.facebook.com/afristablecoin"
                target="_blank"
                rel="noopener noreferrer"
                className="text-slate-400 hover:text-amber-500 transition-colors"
              >
                <Facebook className="w-5 h-5" />
              </a>
              <a
                href="https://www.instagram.com/afristablecoin"
                target="_blank"
                rel="noopener noreferrer"
                className="text-slate-400 hover:text-amber-500 transition-colors"
              >
                <Instagram className="w-5 h-5" />
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-xl font-bold mb-6">Contact Us</h3>
            <ul className="space-y-4">
              <li className="flex items-center gap-3">
                <Mail className="w-5 h-5 text-amber-500" />
                <a
                  href="mailto:<EMAIL>"
                  className="text-slate-400 hover:text-amber-500 transition-colors"
                >
                  events(@)afristablecoin.org
                </a>
              </li>
              <li className="flex items-center gap-3">
                <Globe className="w-5 h-5 text-amber-500" />
                <a
                  href="https://afristablecoin.org"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-slate-400 hover:text-amber-500 transition-colors"
                >
                  afristablecoin.org
                </a>
              </li>
              <li className="text-slate-400">+234 702 599 4794</li>
            </ul>
          </div>
        </div>

        <div className="border-t border-slate-800 pt-8 mt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-slate-500 mb-4 md:mb-0">
              &copy; {new Date().getFullYear()} Africa Stablecoin Network. All
              rights reserved.
            </p>
          </div>

          <p className="text-center text-slate-500 mt-8 text-sm">
            Event Hashtag: #WestAfricaStablecoinSummit #Stablecoinz
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
