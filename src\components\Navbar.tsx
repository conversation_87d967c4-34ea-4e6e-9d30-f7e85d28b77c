import React, { useState } from "react";
import { motion } from "motion/react";
import { Menu, X } from "lucide-react";
import Logo from "./Logo";
import Modal from "./Modal";

const Navbar: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isSponsorModalOpen, setIsSponsorModalOpen] = useState(false);
  const toggleMenu = () => setIsOpen(!isOpen);

  return (
    <>
      <header className="fixed top-0 left-0 right-0 z-50 bg-white shadow-md py-3">
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex justify-between items-center">
            <div className="z-50">
              <Logo className="h-12" />
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-4">
              <a
                href="https://tix.africa/west-africa-stablecoin-summit"
                className="px-5 py-2.5 rounded-md bg-yellow-400 text-black font-medium text-sm hover:bg-yellow-500 border-none transition-all transform hover:scale-105 duration-300"
              >
                Get Tickets
              </a>
              <button
                onClick={() => setIsSponsorModalOpen(true)}
                className="px-5 py-2.5 rounded-md border-2 border-yellow-400 text-yellow-400 font-medium text-sm hover:bg-yellow-400 hover:text-black hover:border-yellow-500 transition-all transform hover:scale-105 duration-300"
              >
                Request Sponsorship Deck
              </button>
            </div>

            {/* Mobile Navigation Toggle */}
            <button
              className="md:hidden text-dark-700 focus:outline-none"
              onClick={toggleMenu}
              aria-label="Toggle menu"
            >
              {isOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        <motion.div
          className={`md:hidden ${isOpen ? "block" : "hidden"}`}
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: isOpen ? 1 : 0, height: isOpen ? "auto" : 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="container mx-auto px-4 py-4 bg-white shadow-lg rounded-b-lg">
            <nav className="flex flex-col space-y-4">
              <a
                href="https://tix.africa/west-africa-stablecoin-summit"
                className="px-5 py-2.5 rounded-md bg-yellow-400 text-black font-medium text-center hover:bg-yellow-500 border-none transition-all"
                onClick={toggleMenu}
              >
                Get Tickets
              </a>
              <button
                className="px-5 py-2.5 rounded-md border-2 border-yellow-400 text-yellow-400 font-medium text-center hover:bg-yellow-400 hover:text-black hover:border-yellow-500 transition-all w-full"
                onClick={() => {
                  setIsSponsorModalOpen(true);
                  toggleMenu();
                }}
              >
                Request Sponsorship Deck
              </button>
            </nav>
          </div>
        </motion.div>
      </header>

      <Modal
        isOpen={isSponsorModalOpen}
        onClose={() => setIsSponsorModalOpen(false)}
      >
        <iframe
          src="https://docs.google.com/forms/d/e/1FAIpQLSd7SzAjSvxA1lOkxH1nqBMkifmbxc3rwtSx_S1kurqFSmA6wA/viewform?embedded=true"
          width="100%"
          height="100%"
          frameBorder="0"
          marginHeight={0}
          marginWidth={0}
        >
          Loading form...
        </iframe>
      </Modal>
    </>
  );
};

export default Navbar;
